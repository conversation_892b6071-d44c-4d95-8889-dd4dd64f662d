/**
 * 图表配置工具函数
 */

/**
 * 获取默认的图表主题色彩
 */
export const getChartColors = () => [
  '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
  '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#5470c6'
]

/**
 * 获取基础图表配置
 */
export const getBaseChartOption = () => ({
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: 'Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif'
  },
  animation: true,
  animationDuration: 1000,
  animationEasing: 'cubicOut'
})

/**
 * 净值走势图配置
 * @param {Array} data 净值数据
 * @param {Object} options 配置选项
 */
export const getNetValueChartOption = (data, options = {}) => {
  const {
    title = '净值走势',
    showDataZoom = true,
    showToolbox = true
  } = options

  return {
    ...getBaseChartOption(),
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        const date = params[0].axisValue
        let html = `<div style="margin-bottom: 5px;">${date}</div>`

        params.forEach(param => {
          const color = param.color
          const name = param.seriesName
          const value = param.value
          html += `
            <div style="display: flex; align-items: center; margin-bottom: 3px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 20px;">${name}:</span>
              <span style="font-weight: bold;">${value}</span>
            </div>
          `
        })

        return html
      }
    },
    legend: {
      top: 30,
      data: data.map(item => item.name)
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: showDataZoom ? '15%' : '3%',
      containLabel: true
    },
    toolbox: showToolbox ? {
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        restore: {
          title: '还原'
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原'
          }
        }
      }
    } : undefined,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data[0]?.data?.map(item => item.date) || [],
      axisLabel: {
        formatter: function(value) {
          return value.slice(5) // 只显示月-日
        }
      }
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: '{value}'
      }
    },
    dataZoom: showDataZoom ? [
      {
        type: 'inside',
        start: 70,
        end: 100
      },
      {
        start: 70,
        end: 100,
        height: 20
      }
    ] : undefined,
    series: data.map((item, index) => ({
      name: item.name,
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: {
        width: 2
      },
      itemStyle: {
        color: getChartColors()[index % getChartColors().length]
      },
      data: item.data?.map(d => d.value) || []
    }))
  }
}

/**
 * 持仓分布饼图配置
 * @param {Array} data 持仓数据
 * @param {Object} options 配置选项
 */
export const getPortfolioPieChartOption = (data, options = {}) => {
  const {
    title = '持仓分布',
    showLegend = true,
    radius = ['40%', '70%']
  } = options

  return {
    ...getBaseChartOption(),
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `
          <div style="margin-bottom: 5px;">${params.name}</div>
          <div style="display: flex; align-items: center;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="margin-right: 20px;">占比:</span>
            <span style="font-weight: bold;">${params.percent}%</span>
          </div>
          <div style="margin-top: 3px;">
            <span style="margin-right: 20px;">数值:</span>
            <span style="font-weight: bold;">${params.value}</span>
          </div>
        `
      }
    },
    legend: showLegend ? {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      formatter: function(name) {
        return name.length > 8 ? name.slice(0, 8) + '...' : name
      }
    } : undefined,
    series: [
      {
        type: 'pie',
        radius: radius,
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: getChartColors()[index % getChartColors().length]
          }
        }))
      }
    ]
  }
}

/**
 * 持仓分布饼图配置
 * @param {Array} data 持仓数据
 * @param {Object} options 配置选项
 */
export const getPortfolioPieChartOption = (data, options = {}) => {
  const {
    title = '持仓分布',
    showLegend = true,
    radius = ['40%', '70%']
  } = options

  return {
    ...getBaseChartOption(),
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `
          <div style="margin-bottom: 5px;">${params.name}</div>
          <div style="display: flex; align-items: center;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="margin-right: 20px;">占比:</span>
            <span style="font-weight: bold;">${params.percent}%</span>
          </div>
          <div style="margin-top: 3px;">
            <span style="margin-right: 20px;">数值:</span>
            <span style="font-weight: bold;">${params.value}</span>
          </div>
        `
      }
    },
    legend: showLegend ? {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      formatter: function(name) {
        return name.length > 8 ? name.slice(0, 8) + '...' : name
      }
    } : undefined,
    series: [
      {
        type: 'pie',
        radius: radius,
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: getChartColors()[index % getChartColors().length]
          }
        }))
      }
    ]
  }
}

/**
 * 业绩对比柱状图配置
 * @param {Array} data 业绩数据
 * @param {Object} options 配置选项
 */
export const getPerformanceBarChartOption = (data, options = {}) => {
  const {
    title = '业绩对比',
    categories = [],
    showDataLabels = true
  } = options

  return {
    ...getBaseChartOption(),
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let html = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`
        
        params.forEach(param => {
          const color = param.color
          const name = param.seriesName
          const value = param.value
          html += `
            <div style="display: flex; align-items: center; margin-bottom: 3px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 2px; margin-right: 8px;"></span>
              <span style="margin-right: 20px;">${name}:</span>
              <span style="font-weight: bold;">${value}%</span>
            </div>
          `
        })
        
        return html
      }
    },
    legend: {
      top: 30,
      data: data.map(item => item.name)
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        interval: 0,
        rotate: categories.length > 6 ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: data.map((item, index) => ({
      name: item.name,
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        color: getChartColors()[index % getChartColors().length],
        borderRadius: [2, 2, 0, 0]
      },
      label: showDataLabels ? {
        show: true,
        position: 'top',
        formatter: '{c}%'
      } : undefined,
      data: item.data || []
    }))
  }
}

/**
 * 风险收益散点图配置
 * @param {Array} data 风险收益数据
 * @param {Object} options 配置选项
 */
export const getRiskReturnScatterOption = (data, options = {}) => {
  const {
    title = '风险收益分析',
    xAxisName = '风险(标准差)',
    yAxisName = '收益率(%)'
  } = options

  return {
    ...getBaseChartOption(),
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const [risk, returnRate] = params.value
        return `
          <div style="margin-bottom: 5px;">${params.seriesName}</div>
          <div style="margin-bottom: 3px;">风险: ${risk}%</div>
          <div>收益: ${returnRate}%</div>
        `
      }
    },
    grid: {
      left: '3%',
      right: '7%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: xAxisName,
      nameLocation: 'middle',
      nameGap: 30,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      nameLocation: 'middle',
      nameGap: 40,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: data.map((item, index) => ({
      name: item.name,
      type: 'scatter',
      symbolSize: 8,
      itemStyle: {
        color: getChartColors()[index % getChartColors().length]
      },
      data: item.data || []
    }))
  }
}

/**
 * 响应式图表配置
 * @param {Object} option 图表配置
 * @param {number} containerWidth 容器宽度
 */
export const getResponsiveChartOption = (option, containerWidth) => {
  const isMobile = containerWidth < 768
  
  if (isMobile) {
    return {
      ...option,
      title: {
        ...option.title,
        textStyle: {
          ...option.title?.textStyle,
          fontSize: 14
        }
      },
      legend: {
        ...option.legend,
        orient: 'horizontal',
        left: 'center',
        top: 'bottom',
        textStyle: {
          fontSize: 12
        }
      },
      grid: {
        ...option.grid,
        left: '5%',
        right: '5%',
        bottom: '20%'
      }
    }
  }
  
  return option
}
