<template>
  <div class="fund-detail-container" v-loading="loading">
    <!-- 基金基本信息卡片 -->
    <div class="basic-info-card card">
      <div class="card-header">
        <h3 class="card-title">基金基本信息</h3>
        <div class="header-actions">
          <el-button @click="addToCompare" :disabled="isInCompare">
            <el-icon><TrendCharts /></el-icon>
            {{ isInCompare ? '已添加对比' : '添加对比' }}
          </el-button>
          <el-button type="primary" @click="exportReport">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
        </div>
      </div>
      
      <div class="basic-info-content" v-if="fundDetail">
        <div class="info-grid">
          <div class="info-item">
            <label>基金代码</label>
            <span>{{ formatFundCode(fundDetail.code) }}</span>
          </div>
          <div class="info-item">
            <label>基金名称</label>
            <span>{{ fundDetail.name }}</span>
          </div>
          <div class="info-item">
            <label>基金经理</label>
            <span>{{ fundDetail.manager }}</span>
          </div>
          <div class="info-item">
            <label>基金类型</label>
            <el-tag :type="getTypeTagType(fundDetail.type)">
              {{ formatFundType(fundDetail.type) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>基金规模</label>
            <span>{{ formatFundScale(fundDetail.scale) }}</span>
          </div>
          <div class="info-item">
            <label>成立日期</label>
            <span>{{ formatDate(fundDetail.establishDate) }}</span>
          </div>
          <div class="info-item">
            <label>单位净值</label>
            <span class="net-value">{{ formatNumber(fundDetail.netValue, 4) }}</span>
          </div>
          <div class="info-item">
            <label>累计净值</label>
            <span>{{ formatNumber(fundDetail.totalNetValue, 4) }}</span>
          </div>
          <div class="info-item">
            <label>日涨跌幅</label>
            <span :class="getReturnClass(fundDetail.dayReturn)">
              {{ formatReturn(fundDetail.dayReturn) }}
            </span>
          </div>
          <div class="info-item">
            <label>近一年收益</label>
            <span :class="getReturnClass(fundDetail.yearReturn)">
              {{ formatReturn(fundDetail.yearReturn) }}
            </span>
          </div>
          <div class="info-item">
            <label>管理费率</label>
            <span>{{ formatPercent(fundDetail.managementFee) }}</span>
          </div>
          <div class="info-item">
            <label>托管费率</label>
            <span>{{ formatPercent(fundDetail.custodyFee) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 净值走势图 -->
        <el-col :span="24" :lg="16">
          <div class="chart-card card">
            <div class="card-header">
              <h3 class="card-title">净值走势</h3>
              <div class="chart-controls">
                <el-radio-group v-model="chartPeriod" @change="updateChart">
                  <el-radio-button label="1M">近一月</el-radio-button>
                  <el-radio-button label="3M">近三月</el-radio-button>
                  <el-radio-button label="6M">近半年</el-radio-button>
                  <el-radio-button label="1Y">近一年</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-container">
              <v-chart 
                ref="netValueChart"
                :option="netValueChartOption" 
                style="height: 400px"
                autoresize
              />
            </div>
          </div>
        </el-col>
        
        <!-- 持仓分布饼图 -->
        <el-col :span="24" :lg="8">
          <div class="chart-card card">
            <div class="card-header">
              <h3 class="card-title">持仓分布</h3>
            </div>
            <div class="chart-container">
              <v-chart 
                ref="portfolioChart"
                :option="portfolioChartOption" 
                style="height: 400px"
                autoresize
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 持仓明细表格 -->
    <div class="portfolio-section">
      <div class="portfolio-card card">
        <div class="card-header">
          <h3 class="card-title">前十大持仓明细</h3>
          <div class="header-actions">
            <el-button @click="exportPortfolio">
              <el-icon><Download /></el-icon>
              导出持仓
            </el-button>
          </div>
        </div>
        
        <el-table
          :data="portfolioData"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
          />
          
          <el-table-column
            prop="stockCode"
            label="股票代码"
            width="100"
          />
          
          <el-table-column
            prop="stockName"
            label="股票名称"
            min-width="150"
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="industry"
            label="所属行业"
            width="120"
            show-overflow-tooltip
          />
          
          <el-table-column
            prop="weight"
            label="持仓比例"
            width="100"
            sortable
          >
            <template #default="{ row }">
              {{ formatPercent(row.weight) }}
            </template>
          </el-table-column>
          
          <el-table-column
            prop="marketValue"
            label="持仓市值(万元)"
            width="130"
            sortable
          >
            <template #default="{ row }">
              {{ formatNumber(row.marketValue, 2) }}
            </template>
          </el-table-column>
          
          <el-table-column
            prop="shares"
            label="持仓股数(万股)"
            width="130"
            sortable
          >
            <template #default="{ row }">
              {{ formatNumber(row.shares, 2) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分析报告区域 -->
    <div class="report-section" v-if="analysisReport">
      <div class="report-card card">
        <div class="card-header">
          <h3 class="card-title">AI分析报告</h3>
          <div class="header-actions">
            <el-button @click="regenerateReport">
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
          </div>
        </div>
        
        <div class="report-content">
          <div class="report-section-item">
            <h4>市场展望</h4>
            <p>{{ analysisReport.marketOutlook }}</p>
          </div>
          
          <div class="report-section-item">
            <h4>运作分析</h4>
            <p>{{ analysisReport.operationAnalysis }}</p>
          </div>
          
          <div class="report-section-item">
            <h4>投资建议</h4>
            <p>{{ analysisReport.investmentAdvice }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { useFundStore } from '@/stores/fund'
import { 
  formatNumber, formatReturn, formatFundCode, 
  formatFundType, formatFundScale, formatDate, formatPercent
} from '@/utils/format'
import { getNetValueChartOption, getPortfolioPieChartOption } from '@/utils/chart'
import { exportFundDetail } from '@/utils/export'
import { TrendCharts, Download, Refresh } from '@element-plus/icons-vue'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent
])

const route = useRoute()
const router = useRouter()
const fundStore = useFundStore()

// 响应式数据
const loading = ref(false)
const fundDetail = ref(null)
const portfolioData = ref([])
const analysisReport = ref(null)
const chartPeriod = ref('6M')

// 计算属性
const fundCode = computed(() => route.params.code)
const isInCompare = computed(() => 
  fundStore.selectedFundCodes.includes(fundCode.value)
)

// 图表配置
const netValueChartOption = computed(() => {
  if (!fundDetail.value?.netValueHistory) return {}
  
  const data = [{
    name: fundDetail.value.name,
    data: fundDetail.value.netValueHistory
  }]
  
  return getNetValueChartOption(data, {
    title: '净值走势图',
    showDataZoom: true,
    showToolbox: true
  })
})

const portfolioChartOption = computed(() => {
  if (!portfolioData.value?.length) return {}
  
  const data = portfolioData.value.slice(0, 10).map(item => ({
    name: item.stockName,
    value: item.weight
  }))
  
  return getPortfolioPieChartOption(data, {
    title: '前十大持仓分布',
    showLegend: true
  })
})

// 方法
const fetchFundDetail = async () => {
  loading.value = true
  try {
    const detail = await fundStore.fetchFundDetail(fundCode.value)
    fundDetail.value = detail.basicInfo
    portfolioData.value = detail.portfolio || []
    analysisReport.value = detail.analysisReport
  } catch (error) {
    ElMessage.error('获取基金详情失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const addToCompare = () => {
  if (isInCompare.value) return
  
  try {
    fundStore.addToCompare(fundDetail.value)
    ElMessage.success(`已添加 ${fundDetail.value.name} 到对比列表`)
  } catch (error) {
    ElMessage.warning(error.message)
  }
}

const exportReport = async () => {
  try {
    await exportFundDetail({
      basicInfo: fundDetail.value,
      portfolio: portfolioData.value,
      performance: fundDetail.value.netValueHistory
    }, 'excel')
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  }
}

const exportPortfolio = async () => {
  try {
    await exportFundDetail({
      basicInfo: fundDetail.value,
      portfolio: portfolioData.value
    }, 'csv')
    ElMessage.success('持仓数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  }
}

const regenerateReport = () => {
  ElMessage.info('重新生成报告功能开发中...')
}

const updateChart = () => {
  // 根据选择的时间周期更新图表数据
  ElMessage.info(`切换到${chartPeriod.value}周期`)
}

// 工具函数
const getTypeTagType = (type) => {
  const typeMap = {
    'stock': 'danger',
    'bond': 'success', 
    'hybrid': 'warning',
    'money': 'info',
    'index': 'primary'
  }
  return typeMap[type] || ''
}

const getReturnClass = (value) => {
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-danger'
  return 'text-regular'
}

// 监听器
watch(
  () => route.params.code,
  (newCode) => {
    if (newCode) {
      fetchFundDetail()
    }
  }
)

// 生命周期
onMounted(() => {
  fetchFundDetail()
})
</script>

<style scoped>
.fund-detail-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.basic-info-card {
  background: var(--bg-color);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.info-item label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.info-item span {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.net-value {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.charts-section {
  margin: var(--spacing-lg) 0;
}

.chart-card {
  background: var(--bg-color);
  height: 100%;
}

.chart-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.chart-container {
  padding: var(--spacing-md);
}

.portfolio-section,
.report-section {
  background: var(--bg-color);
}

.report-content {
  padding: var(--spacing-md);
}

.report-section-item {
  margin-bottom: var(--spacing-lg);
}

.report-section-item h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-md);
  font-weight: 600;
}

.report-section-item p {
  color: var(--text-regular);
  line-height: 1.6;
  text-align: justify;
}

.text-success {
  color: var(--success-color);
  font-weight: 600;
}

.text-danger {
  color: var(--danger-color);
  font-weight: 600;
}

.text-regular {
  color: var(--text-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-controls {
    flex-direction: column;
  }
  
  .header-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>
