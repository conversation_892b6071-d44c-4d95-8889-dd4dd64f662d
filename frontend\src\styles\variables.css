/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #409eff;
  --primary-color-hover: #66b1ff;
  --primary-color-active: #3a8ee6;
  --primary-color-light: #ecf5ff;
  
  --success-color: #67c23a;
  --success-color-hover: #85ce61;
  --success-color-light: #f0f9ff;
  
  --warning-color: #e6a23c;
  --warning-color-hover: #ebb563;
  --warning-color-light: #fdf6ec;
  
  --danger-color: #f56c6c;
  --danger-color-hover: #f78989;
  --danger-color-light: #fef0f0;
  
  --info-color: #909399;
  --info-color-hover: #a6a9ad;
  --info-color-light: #f4f4f5;

  /* 文字颜色 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  --text-disabled: #c0c4cc;

  /* 边框颜色 */
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;

  /* 背景颜色 */
  --bg-color: #ffffff;
  --bg-page: #f2f3f5;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  --bg-mask: rgba(255, 255, 255, 0.9);

  /* 字体 */
  --font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* 圆角 */
  --border-radius: 4px;
  --border-radius-sm: 2px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-round: 50%;

  /* 阴影 */
  --box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);

  /* 过渡动画 */
  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  --transition-slide: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);

  /* Z-index层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-top: 9999;

  /* 布局尺寸 */
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --footer-height: 60px;

  /* 表格相关 */
  --table-header-bg: #fafafa;
  --table-row-hover-bg: #f5f7fa;
  --table-border-color: var(--border-lighter);

  /* 图表颜色 */
  --chart-color-1: #5470c6;
  --chart-color-2: #91cc75;
  --chart-color-3: #fac858;
  --chart-color-4: #ee6666;
  --chart-color-5: #73c0de;
  --chart-color-6: #3ba272;
  --chart-color-7: #fc8452;
  --chart-color-8: #9a60b4;
  --chart-color-9: #ea7ccc;
  --chart-color-10: #5470c6;
}

/* 暗色主题 */
[data-theme='dark'] {
  --text-primary: #e5eaf3;
  --text-regular: #cfd3dc;
  --text-secondary: #a3a6ad;
  --text-placeholder: #8d9095;
  --text-disabled: #6c6e72;

  --border-base: #4c4d4f;
  --border-light: #414243;
  --border-lighter: #363637;
  --border-extra-light: #2b2b2c;

  --bg-color: #1d1e1f;
  --bg-page: #141414;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  --bg-mask: rgba(0, 0, 0, 0.9);

  --table-header-bg: #262727;
  --table-row-hover-bg: #262727;
}

/* 响应式断点 */
@custom-media --mobile (max-width: 767px);
@custom-media --tablet (min-width: 768px) and (max-width: 1023px);
@custom-media --desktop (min-width: 1024px);
@custom-media --large-desktop (min-width: 1200px);

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-regular { color: var(--text-regular); }
.text-secondary { color: var(--text-secondary); }
.text-placeholder { color: var(--text-placeholder); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }

.border-primary { border-color: var(--primary-color); }
.border-success { border-color: var(--success-color); }
.border-warning { border-color: var(--warning-color); }
.border-danger { border-color: var(--danger-color); }

.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }
.flex-column { display: flex; flex-direction: column; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-md); }
.pt-4 { padding-top: var(--spacing-lg); }
