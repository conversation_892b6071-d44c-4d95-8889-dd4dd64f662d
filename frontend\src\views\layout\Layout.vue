<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="layout-header">
      <div class="header-left">
        <el-button
          type="text"
          @click="toggleSidebar"
          class="sidebar-toggle"
        >
          <el-icon><Menu /></el-icon>
        </el-button>
        <h1 class="app-title">基金分析系统</h1>
      </div>
      
      <div class="header-right">
        <el-button type="text" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        
        <el-dropdown>
          <el-button type="text">
            <el-icon><Setting /></el-icon>
            设置
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="toggleTheme">
                <el-icon><Moon /></el-icon>
                切换主题
              </el-dropdown-item>
              <el-dropdown-item>
                <el-icon><QuestionFilled /></el-icon>
                帮助文档
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="layout-body">
      <!-- 侧边栏 -->
      <el-aside 
        :width="sidebarWidth" 
        class="layout-sidebar"
        :class="{ 'is-collapsed': sidebarCollapsed }"
      >
        <el-menu
          :default-active="activeMenu"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/funds">
            <el-icon><List /></el-icon>
            <template #title>基金列表</template>
          </el-menu-item>
          
          <el-menu-item index="/compare">
            <el-icon><TrendCharts /></el-icon>
            <template #title>基金对比</template>
          </el-menu-item>
          
          <el-menu-item index="/reports">
            <el-icon><Files /></el-icon>
            <template #title>报告中心</template>
          </el-menu-item>
          
          <el-sub-menu index="tools">
            <template #title>
              <el-icon><Tools /></el-icon>
              <span>工具箱</span>
            </template>
            <el-menu-item index="/tools/export">
              <el-icon><Download /></el-icon>
              <template #title>数据导出</template>
            </el-menu-item>
            <el-menu-item index="/tools/import">
              <el-icon><Upload /></el-icon>
              <template #title>数据导入</template>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="layout-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container" v-if="breadcrumbs.length > 0">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item 
              v-for="item in breadcrumbs" 
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 页面内容 -->
        <div class="page-content">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </el-container>

    <!-- 全局加载遮罩 -->
    <div v-if="loading" class="global-loading">
      <el-loading-spinner />
      <p>数据加载中...</p>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import {
  Menu, Refresh, Setting, Moon, QuestionFilled,
  List, TrendCharts, Files, Tools, Download, Upload
} from '@element-plus/icons-vue'

const route = useRoute()
const appStore = useAppStore()

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const sidebarWidth = computed(() => sidebarCollapsed.value ? '64px' : '240px')
const loading = computed(() => appStore.loading)
const breadcrumbs = computed(() => appStore.breadcrumbs)

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/funds')) return '/funds'
  if (path.startsWith('/compare')) return '/compare'
  if (path.startsWith('/reports')) return '/reports'
  return path
})

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const refreshData = () => {
  // 触发数据刷新
  appStore.setLoading(true)
  setTimeout(() => {
    appStore.setLoading(false)
    ElMessage.success('数据刷新完成')
  }, 1000)
}

const toggleTheme = () => {
  const currentTheme = appStore.theme
  const newTheme = currentTheme === 'light' ? 'dark' : 'light'
  appStore.setTheme(newTheme)
  ElMessage.success(`已切换到${newTheme === 'light' ? '浅色' : '深色'}主题`)
}

// 监听路由变化，更新面包屑
watch(
  () => route.path,
  (newPath) => {
    const breadcrumbMap = {
      '/funds': [{ title: '基金列表', path: '/funds' }],
      '/compare': [{ title: '基金对比', path: '/compare' }],
      '/reports': [{ title: '报告中心', path: '/reports' }]
    }
    
    // 处理基金详情页面
    if (newPath.startsWith('/funds/') && newPath !== '/funds') {
      const fundCode = newPath.split('/')[2]
      breadcrumbMap[newPath] = [
        { title: '基金列表', path: '/funds' },
        { title: `基金详情 (${fundCode})`, path: newPath }
      ]
    }
    
    appStore.setBreadcrumbs(breadcrumbMap[newPath] || [])
  },
  { immediate: true }
)

// 响应式处理
const handleResize = () => {
  const width = window.innerWidth
  if (width < 768) {
    appStore.setDevice('mobile')
    appStore.setSidebarCollapsed(true)
  } else if (width < 1024) {
    appStore.setDevice('tablet')
  } else {
    appStore.setDevice('desktop')
  }
}

// 监听窗口大小变化
window.addEventListener('resize', handleResize)
handleResize() // 初始化
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--spacing-lg);
  height: var(--header-height);
  box-shadow: var(--box-shadow-light);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.sidebar-toggle {
  font-size: 18px;
  color: var(--text-regular);
}

.app-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.layout-body {
  flex: 1;
  overflow: hidden;
}

.layout-sidebar {
  background: var(--bg-color);
  border-right: 1px solid var(--border-light);
  transition: width var(--transition-base);
  overflow: hidden;
}

.layout-sidebar.is-collapsed {
  width: 64px !important;
}

.sidebar-menu {
  border: none;
  height: 100%;
}

.layout-main {
  background: var(--bg-page);
  padding: 0;
  overflow-y: auto;
}

.breadcrumb-container {
  background: var(--bg-color);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-lighter);
}

.page-content {
  padding: var(--spacing-lg);
  min-height: calc(100vh - var(--header-height) - 60px);
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: white;
}

.global-loading p {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-md);
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 var(--spacing-md);
  }
  
  .app-title {
    font-size: var(--font-size-md);
  }
  
  .page-content {
    padding: var(--spacing-md);
  }
  
  .breadcrumb-container {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
</style>
