#!/usr/bin/env python3
"""
全面验证quarterly_data_config.json配置文件功能
"""

import logging
import os
import json
import shutil
import time
from dotenv import load_dotenv
from src.data_fetcher import (
    fetch_fund_data, 
    fetch_fund_data_with_config, 
    fetch_fund_portfolio_data,
    get_quarterly_data_config
)

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def backup_config_file():
    """备份原始配置文件"""
    config_path = "config/quarterly_data_config.json"
    backup_path = "config/quarterly_data_config.json.backup"
    
    if os.path.exists(config_path):
        shutil.copy2(config_path, backup_path)
        return True
    return False

def restore_config_file():
    """恢复原始配置文件"""
    config_path = "config/quarterly_data_config.json"
    backup_path = "config/quarterly_data_config.json.backup"
    
    if os.path.exists(backup_path):
        shutil.copy2(backup_path, config_path)
        os.remove(backup_path)
        return True
    return False

def create_test_config(config_data):
    """创建测试配置文件"""
    config_path = "config/quarterly_data_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)

def test_config_file_format():
    """测试配置文件格式验证"""
    logger = logging.getLogger(__name__)
    
    logger.info("=== 1. 配置文件格式验证 ===")
    
    config_path = "config/quarterly_data_config.json"
    
    # 检查文件是否存在
    if not os.path.exists(config_path):
        logger.error(f"❌ 配置文件不存在: {config_path}")
        return False
    
    logger.info(f"✅ 配置文件存在: {config_path}")
    
    # 检查JSON格式
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        logger.info("✅ JSON格式正确")
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 读取配置文件失败: {e}")
        return False
    
    # 检查必要字段
    logger.info("检查配置文件结构:")
    logger.info(f"  完整配置: {json.dumps(config_data, indent=2, ensure_ascii=False)}")
    
    if "quarterly_data" in config_data:
        quarterly_config = config_data["quarterly_data"]
        logger.info("✅ 包含 quarterly_data 字段")
        
        required_fields = ["default_year", "default_quarter"]
        for field in required_fields:
            if field in quarterly_config:
                logger.info(f"✅ 包含 {field} 字段: {quarterly_config[field]}")
            else:
                logger.warning(f"⚠️  缺少 {field} 字段")
    else:
        logger.error("❌ 缺少 quarterly_data 字段")
        return False
    
    return True

def test_config_reading_function():
    """测试配置读取功能"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n=== 2. 配置读取功能验证 ===")
    
    # 测试当前配置
    logger.info("测试当前配置读取:")
    year, quarter = get_quarterly_data_config()
    logger.info(f"读取结果: year={year}, quarter={quarter}")
    
    if year is None and quarter is None:
        logger.info("✅ 正确读取到 null 配置（使用最新数据）")
    elif isinstance(year, int) and isinstance(quarter, int) and 1 <= quarter <= 4:
        logger.info(f"✅ 正确读取到具体配置: {year}年Q{quarter}")
    else:
        logger.warning(f"⚠️  读取到异常配置: year={year}, quarter={quarter}")
    
    return year, quarter

def test_specific_quarter_config():
    """测试特定季度配置"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n=== 3. 特定季度配置测试 ===")
    
    # 备份原始配置
    backup_success = backup_config_file()
    if not backup_success:
        logger.warning("⚠️  无法备份原始配置文件")
    
    test_configs = [
        {
            "name": "2025年Q1配置",
            "config": {
                "quarterly_data": {
                    "description": "测试配置 - 2025年第一季度",
                    "version": "1.0.0",
                    "default_year": 2025,
                    "default_quarter": 1
                }
            },
            "expected_date": "2025-03-31"
        },
        {
            "name": "2024年Q4配置", 
            "config": {
                "quarterly_data": {
                    "description": "测试配置 - 2024年第四季度",
                    "version": "1.0.0",
                    "default_year": 2024,
                    "default_quarter": 4
                }
            },
            "expected_date": "2024-12-31"
        }
    ]
    
    test_fund = "163302.OF"  # 使用已知有数据的基金
    
    for test_case in test_configs:
        logger.info(f"\n测试 {test_case['name']}:")
        
        # 创建测试配置
        create_test_config(test_case['config'])
        
        # 验证配置读取
        year, quarter = get_quarterly_data_config()
        expected_year = test_case['config']['quarterly_data']['default_year']
        expected_quarter = test_case['config']['quarterly_data']['default_quarter']
        
        if year == expected_year and quarter == expected_quarter:
            logger.info(f"✅ 配置读取正确: {year}年Q{quarter}")
        else:
            logger.error(f"❌ 配置读取错误: 期望{expected_year}年Q{expected_quarter}, 实际{year}年Q{quarter}")
            continue
        
        # 测试基金数据获取
        logger.info(f"测试基金 {test_fund} 的数据获取:")
        
        start_time = time.time()
        fund_data = fetch_fund_data_with_config(test_fund)
        config_time = time.time() - start_time
        
        if fund_data is None:
            logger.warning(f"⚠️  无法获取基金 {test_fund} 在 {year}年Q{quarter} 的数据")
            continue
        
        actual_date = fund_data.get('report_end_date')
        expected_date = test_case['expected_date']
        
        logger.info(f"  获取耗时: {config_time:.3f}秒")
        logger.info(f"  基金名称: {fund_data.get('fund_name')}")
        logger.info(f"  期望报告期: {expected_date}")
        logger.info(f"  实际报告期: {actual_date}")
        
        if actual_date == expected_date:
            logger.info(f"✅ 报告期匹配正确")
        else:
            logger.warning(f"⚠️  报告期不匹配，可能该季度无数据")
        
        # 测试持仓数据获取（含转换）
        if actual_date and actual_date != "未知报告日期":
            logger.info(f"测试持仓数据获取:")
            start_time = time.time()
            portfolio_data = fetch_fund_portfolio_data(test_fund, actual_date)
            portfolio_time = time.time() - start_time
            
            logger.info(f"  持仓查询耗时: {portfolio_time:.3f}秒")
            
            if portfolio_data is None:
                logger.info(f"  ⚠️  无持仓数据")
            elif not portfolio_data:
                logger.info(f"  ℹ️  持仓数据为空")
            else:
                logger.info(f"  ✅ 成功获取 {len(portfolio_data)} 条持仓数据")
                if len(portfolio_data) > 0:
                    logger.info(f"    第一大持仓: {portfolio_data[0].get('symbol')} - {portfolio_data[0].get('stock_name')}")
    
    # 恢复原始配置
    restore_success = restore_config_file()
    if restore_success:
        logger.info("\n✅ 已恢复原始配置文件")
    else:
        logger.warning("\n⚠️  无法恢复原始配置文件")

def test_error_handling():
    """测试错误处理"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n=== 4. 错误处理测试 ===")
    
    # 备份原始配置
    backup_success = backup_config_file()
    
    error_test_cases = [
        {
            "name": "配置文件不存在",
            "action": "delete_file",
            "config": None
        },
        {
            "name": "JSON格式错误",
            "action": "create_invalid_json",
            "config": "{ invalid json format"
        },
        {
            "name": "缺少必要字段",
            "action": "create_incomplete_config",
            "config": {
                "other_data": {
                    "some_field": "some_value"
                }
            }
        },
        {
            "name": "无效的季度值",
            "action": "create_invalid_quarter",
            "config": {
                "quarterly_data": {
                    "default_year": 2025,
                    "default_quarter": 5  # 无效季度
                }
            }
        }
    ]
    
    config_path = "config/quarterly_data_config.json"
    
    for test_case in error_test_cases:
        logger.info(f"\n测试 {test_case['name']}:")
        
        # 执行测试操作
        if test_case['action'] == 'delete_file':
            if os.path.exists(config_path):
                os.remove(config_path)
            logger.info("  已删除配置文件")
            
        elif test_case['action'] == 'create_invalid_json':
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(test_case['config'])
            logger.info("  已创建无效JSON文件")
            
        elif test_case['action'] == 'create_incomplete_config':
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(test_case['config'], f, indent=2)
            logger.info("  已创建不完整配置文件")
            
        elif test_case['action'] == 'create_invalid_quarter':
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(test_case['config'], f, indent=2)
            logger.info("  已创建无效季度配置文件")
        
        # 测试配置读取
        try:
            year, quarter = get_quarterly_data_config()
            logger.info(f"  配置读取结果: year={year}, quarter={quarter}")
            
            if year is None and quarter is None:
                logger.info("  ✅ 正确处理错误，回退到默认行为（最新数据）")
            else:
                logger.warning(f"  ⚠️  意外的读取结果: {year}, {quarter}")
                
        except Exception as e:
            logger.info(f"  ✅ 正确抛出异常: {e}")
        
        # 测试基金数据获取是否仍能工作
        try:
            fund_data = fetch_fund_data_with_config("163302.OF")
            if fund_data is not None:
                logger.info("  ✅ 基金数据获取功能在错误情况下仍能正常工作")
            else:
                logger.warning("  ⚠️  基金数据获取功能受到影响")
        except Exception as e:
            logger.warning(f"  ⚠️  基金数据获取出现异常: {e}")
    
    # 恢复原始配置
    restore_success = restore_config_file()
    if restore_success:
        logger.info("\n✅ 已恢复原始配置文件")

def test_data_consistency_with_conversion():
    """测试配置文件与代码转换功能的数据一致性"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n=== 5. 数据一致性与转换功能测试 ===")
    
    # 备份并设置测试配置
    backup_config_file()
    
    test_config = {
        "quarterly_data": {
            "description": "数据一致性测试配置",
            "version": "1.0.0", 
            "default_year": 2025,
            "default_quarter": 1
        }
    }
    
    create_test_config(test_config)
    logger.info("设置测试配置: 2025年Q1")
    
    test_funds = ["169102.OF", "163302.OF"]
    
    for fund_code in test_funds:
        logger.info(f"\n测试基金: {fund_code}")
        
        # 获取基金基本信息
        fund_data = fetch_fund_data_with_config(fund_code)
        if fund_data is None:
            logger.warning(f"  ⚠️  无法获取基金基本信息")
            continue
        
        report_date = fund_data.get('report_end_date')
        logger.info(f"  配置获取的报告期: {report_date}")
        
        # 测试持仓数据获取（含转换）
        portfolio_data = fetch_fund_portfolio_data(fund_code, report_date)
        
        if portfolio_data is None:
            logger.info(f"  ⚠️  无持仓数据（包括转换尝试）")
        elif not portfolio_data:
            logger.info(f"  ℹ️  持仓数据为空")
        else:
            logger.info(f"  ✅ 成功获取 {len(portfolio_data)} 条持仓数据")
            logger.info(f"    数据一致性: 基本信息和持仓数据都使用报告期 {report_date}")
    
    # 恢复原始配置
    restore_config_file()
    logger.info("\n✅ 已恢复原始配置文件")

def main():
    """主测试函数"""
    setup_logging()
    load_dotenv()
    
    logger = logging.getLogger(__name__)
    
    logger.info("开始quarterly_data_config.json配置文件全面验证")
    
    try:
        # 1. 配置文件格式验证
        format_ok = test_config_file_format()
        
        # 2. 配置读取功能验证
        if format_ok:
            test_config_reading_function()
        
        # 3. 特定季度配置测试
        test_specific_quarter_config()
        
        # 4. 错误处理测试
        test_error_handling()
        
        # 5. 数据一致性测试
        test_data_consistency_with_conversion()
        
        logger.info("\n=== 测试完成 ===")
        logger.info("所有配置文件功能验证已完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
