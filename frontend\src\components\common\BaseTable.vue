<template>
  <div class="base-table-container">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <span class="table-title">{{ title }}</span>
        </slot>
      </div>
      
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <el-button 
            v-if="showRefresh"
            size="small" 
            @click="handleRefresh"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          
          <el-button 
            v-if="showExport"
            size="small" 
            @click="handleExport"
          >
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </slot>
      </div>
    </div>

    <!-- 表格主体 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="data"
      :stripe="stripe"
      :border="border"
      :size="size"
      :height="height"
      :max-height="maxHeight"
      :show-header="showHeader"
      :highlight-current-row="highlightCurrentRow"
      :row-class-name="rowClassName"
      :empty-text="emptyText"
      @selection-change="handleSelectionChange"
      @current-change="handleCurrentChange"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDblclick"
      @sort-change="handleSortChange"
      v-bind="$attrs"
    >
      <!-- 选择列 -->
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
        :selectable="selectable"
      />
      
      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        label="序号"
        width="60"
        :index="indexMethod"
      />
      
      <!-- 动态列 -->
      <template v-for="column in columns" :key="column.prop">
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip"
          :align="column.align"
          :header-align="column.headerAlign"
        >
          <template #default="scope" v-if="column.slot">
            <slot 
              :name="column.slot" 
              :row="scope.row" 
              :column="scope.column" 
              :$index="scope.$index"
            />
          </template>
          
          <template #default="scope" v-else-if="column.formatter">
            <span>{{ column.formatter(scope.row, scope.column, scope.row[column.prop], scope.$index) }}</span>
          </template>
          
          <template #header="scope" v-if="column.headerSlot">
            <slot 
              :name="column.headerSlot" 
              :column="scope.column" 
              :$index="scope.$index"
            />
          </template>
        </el-table-column>
      </template>
      
      <!-- 操作列 -->
      <el-table-column
        v-if="showActions"
        label="操作"
        :width="actionsWidth"
        :fixed="actionsFixed"
        align="center"
      >
        <template #default="scope">
          <slot 
            name="actions" 
            :row="scope.row" 
            :$index="scope.$index"
          >
            <el-button 
              type="primary" 
              size="small"
              @click="handleEdit(scope.row, scope.$index)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small"
              @click="handleDelete(scope.row, scope.$index)"
            >
              删除
            </el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div v-if="showPagination" class="table-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        :background="paginationBackground"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Refresh, Download } from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  
  // 表格列配置
  columns: {
    type: Array,
    default: () => []
  },
  
  // 表格标题
  title: {
    type: String,
    default: ''
  },
  
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  
  // 表格样式
  stripe: {
    type: Boolean,
    default: true
  },
  
  border: {
    type: Boolean,
    default: true
  },
  
  size: {
    type: String,
    default: 'default'
  },
  
  height: {
    type: [String, Number],
    default: undefined
  },
  
  maxHeight: {
    type: [String, Number],
    default: undefined
  },
  
  showHeader: {
    type: Boolean,
    default: true
  },
  
  highlightCurrentRow: {
    type: Boolean,
    default: false
  },
  
  rowClassName: {
    type: [String, Function],
    default: ''
  },
  
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  
  // 功能开关
  showToolbar: {
    type: Boolean,
    default: true
  },
  
  showSelection: {
    type: Boolean,
    default: false
  },
  
  showIndex: {
    type: Boolean,
    default: false
  },
  
  showActions: {
    type: Boolean,
    default: false
  },
  
  showRefresh: {
    type: Boolean,
    default: true
  },
  
  showExport: {
    type: Boolean,
    default: true
  },
  
  showPagination: {
    type: Boolean,
    default: true
  },
  
  // 选择相关
  selectable: {
    type: Function,
    default: () => true
  },
  
  // 序号相关
  indexMethod: {
    type: Function,
    default: (index) => index + 1
  },
  
  // 操作列配置
  actionsWidth: {
    type: [String, Number],
    default: 150
  },
  
  actionsFixed: {
    type: [String, Boolean],
    default: 'right'
  },
  
  // 分页配置
  total: {
    type: Number,
    default: 0
  },
  
  currentPage: {
    type: Number,
    default: 1
  },
  
  pageSize: {
    type: Number,
    default: 20
  },
  
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  
  paginationBackground: {
    type: Boolean,
    default: true
  }
})

// Emits定义
const emit = defineEmits([
  'selection-change',
  'current-change', 
  'row-click',
  'row-dblclick',
  'sort-change',
  'size-change',
  'current-change',
  'refresh',
  'export',
  'edit',
  'delete'
])

// 表格引用
const tableRef = ref()

// 事件处理
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleCurrentChange = (currentRow, oldCurrentRow) => {
  emit('current-change', currentRow, oldCurrentRow)
}

const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event)
}

const handleRowDblclick = (row, column, event) => {
  emit('row-dblclick', row, column, event)
}

const handleSortChange = ({ column, prop, order }) => {
  emit('sort-change', { column, prop, order })
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}

const handleCurrentPageChange = (page) => {
  emit('current-change', page)
}

const handleRefresh = () => {
  emit('refresh')
}

const handleExport = () => {
  emit('export')
}

const handleEdit = (row, index) => {
  emit('edit', row, index)
}

const handleDelete = (row, index) => {
  emit('delete', row, index)
}

// 暴露方法
const clearSelection = () => {
  tableRef.value?.clearSelection()
}

const toggleRowSelection = (row, selected) => {
  tableRef.value?.toggleRowSelection(row, selected)
}

const toggleAllSelection = () => {
  tableRef.value?.toggleAllSelection()
}

const setCurrentRow = (row) => {
  tableRef.value?.setCurrentRow(row)
}

const clearSort = () => {
  tableRef.value?.clearSort()
}

const doLayout = () => {
  tableRef.value?.doLayout()
}

defineExpose({
  clearSelection,
  toggleRowSelection,
  toggleAllSelection,
  setCurrentRow,
  clearSort,
  doLayout,
  tableRef
})
</script>

<style scoped>
.base-table-container {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-lighter);
  background: var(--bg-page);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.table-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.table-pagination {
  display: flex;
  justify-content: center;
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-lighter);
  background: var(--bg-page);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .table-pagination {
    padding: var(--spacing-md);
  }
}
</style>
