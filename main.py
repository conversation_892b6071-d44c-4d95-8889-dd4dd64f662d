import logging
import argparse # 将在后续迭代中用于命令行参数
import os
from dotenv import load_dotenv # 用于加载 .env 文件

# from src.config_loader import load_config # 假设的配置加载模块
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data, read_fund_list_from_file, fetch_batch_fund_data, get_fund_sw_industry_info, get_fund_hk_sw_industry_info, get_fund_valuation_summary # 假设的数据获取模块
from src.llm_analyzer import analyze_with_llm # 移除了 load_analysis_rules 因为它已在 main.py 中定义
from src.report_generator import save_report_md, save_report_csv, validate_report_format
from src.concept_extractor import get_concept_statistics # 保留统计功能，移除已废弃的文本提取功能
from src.parallel_processor import SimpleLLMParallelProcessor, create_analysis_task # 新增：并行处理器
from src.config_manager import get_parallel_config # 新增：配置管理器
import datetime # 用于生成摘要文件名

def load_analysis_rules(filepath: str = "docs/深度分析专精.md") -> str:
    """
    加载分析规则文件内容。
    如果文件未找到或为空，则记录错误并返回空字符串。
    """
    logger = logging.getLogger(__name__) # 使用 __name__ 获取当前模块的logger
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
            if not content.strip(): # 检查内容是否为空或仅包含空白
                logger.error(f"分析规则文件 '{filepath}' 内容为空。")
                return ""
            logger.info(f"分析规则文件 '{filepath}' 加载成功。")
            return content
    except FileNotFoundError:
        logger.error(f"分析规则文件 '{filepath}' 未找到。")
        return ""
    except Exception as e: # 捕获其他可能的读取错误
        logger.error(f"加载分析规则文件 '{filepath}' 时发生错误: {e}", exc_info=True)
        return ""

def setup_logging():
    """配置日志记录器"""
    # MVP阶段的日志配置 (基于IMP第5节)
    # 后续可以从配置文件加载更复杂的日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(),
            # logging.FileHandler("app.log", encoding='utf-8') # MVP阶段暂时只输出到控制台，后续可启用文件日志
        ]
    )
    # logging.getLogger("httpx").setLevel(logging.WARNING) # 减少第三方库的冗余日志

def run_analysis(fund_code: str):
    """
    执行单个基金的分析流程。
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始分析基金: {fund_code}")

    # 1. 加载配置 (IMP 第4节)
    load_dotenv() # 加载 .env 文件中的环境变量
    api_key = os.getenv("LLM_API_KEY")
    model_name = os.getenv("LLM_MODEL_NAME")
    db_config = {
        "host": os.getenv("DB_HOST"),
        "port": os.getenv("DB_PORT"),
        "name": os.getenv("DB_NAME"),
        "user": os.getenv("DB_USER"),
        "password": os.getenv("DB_PASSWORD"),
    }
    logger.info("配置已加载。") # 修改日志消息
    if not api_key or not model_name: # 增加对model_name的检查
        logger.critical("LLM_API_KEY 或 LLM_MODEL_NAME 未在 .env 文件中正确配置。程序终止。")
        return
    # 简单的数据库配置检查 (可选，但推荐)
    if not all([db_config["host"], db_config["port"], db_config["name"], db_config["user"]]): # 密码可以是空
        logger.warning("数据库连接参数部分缺失，请检查 .env 文件。")
        # 根据实际需求，这里可以选择终止或继续（如果某些操作不需要数据库）
        # for MVP, if DB is essential, we might choose to return here.
        # logger.critical("数据库连接参数不完整。程序终止。")
        # return

    # 2. 加载分析规则 (IMP 3.3节)
    analysis_rules_content = load_analysis_rules() # 调用函数
    if not analysis_rules_content:
        logger.error("未能加载分析规则，基金分析终止。")
        return

    # 3. 获取基金数据 (IMP 3.2节)
    logger.info(f"正在获取基金 {fund_code} 的数据...")
    fund_data_raw = fetch_fund_data(fund_code) # 调用函数 (已移除 db_config 参数)

    if fund_data_raw is None: # fetch_fund_data 内部已按策略处理日志和返回
        logger.error(f"由于未能获取数据或数据不完整，基金 {fund_code} 的分析已终止。")
        return

    # 从获取的数据中提取字段
    fund_name = fund_data_raw.get("fund_name", "未知基金名称")
    fund_manager = fund_data_raw.get("fund_manager", "未知基金经理")
    market_outlook = fund_data_raw.get("market_outlook", "")
    operation_analysis = fund_data_raw.get("operation_analysis", "")
    report_end_date = fund_data_raw.get("report_end_date") # 获取报告期截止日
    # actual_report_date = fund_data_raw.get("actual_report_date", "未知报告日期") # 已移除

    # 根据IMP 3.2中对数据不完整的处理策略，即使字段为空，我们也会记录警告并继续
    if not market_outlook:
        logger.warning(f"基金 {fund_code} 的 'market_outlook' 数据为空。")
    if not operation_analysis:
        logger.warning(f"基金 {fund_code} 的 'operation_analysis' 数据为空。")
    if fund_name == "未知基金名称":
        logger.warning(f"基金 {fund_code} 的 'fund_name' 数据为空或未获取到。")
    if fund_manager == "未知基金经理":
        logger.warning(f"基金 {fund_code} 的 'fund_manager' 数据为空或未获取到。")
    if not report_end_date:
        logger.error(f"未能获取基金 {fund_code} 的报告期截止日 (report_end_date)。无法获取持仓数据，分析终止。")
        return
    # if actual_report_date == "未知报告日期": # 已移除
        # logger.warning(f"基金 {fund_code} 的 'actual_report_date' 数据为空或未获取到。")

    logger.info(f"基金 {fund_code} ({fund_name}) 季报文本数据获取阶段完成。报告期截止日: {report_end_date}")

    # 3.1 获取基金持仓数据
    logger.info(f"正在获取基金 {fund_code} 在 {report_end_date} 的前十大持仓数据...")
    portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)

    if portfolio_data is None:
        logger.warning(f"未能获取基金 {fund_code} 在 {report_end_date} 的持仓数据，或持仓数据为空。分析将不包含持仓信息。")
        # 根据策略，即使没有持仓数据，也可能继续分析，LLM prompt需要能处理这种情况
        portfolio_data_str = "未获取到持仓数据。" # 或者传递一个空列表/字典，由LLM处理
    elif not portfolio_data: # 检查是否为空列表
        logger.info(f"基金 {fund_code} 在 {report_end_date} 的持仓数据为空列表。")
        portfolio_data_str = "报告期内无持仓数据或未披露。"
    else:
        # 将持仓数据转换为字符串格式，包含股票代码、名称、行业、总市值(亿元)、市值风格和占比信息
        formatted_portfolio_items = []
        for item in portfolio_data:
            total_mv_wanyuan = item.get('total_mv')
            if total_mv_wanyuan is not None:
                try:
                    # 尝试将 total_mv_wanyuan 转换为浮点数
                    total_mv_float = float(total_mv_wanyuan)
                    total_mv_yiyuan = total_mv_float / 10000
                    total_mv_str = f"{total_mv_yiyuan:.2f}亿元"
                except (ValueError, TypeError):
                    logger.warning(f"股票 {item.get('symbol')} 的 total_mv ('{total_mv_wanyuan}') 格式无效，无法转换为亿元。")
                    total_mv_str = "无效市值数据" # 或者 "未知市值"，根据需求
            else:
                total_mv_str = "未知市值"

            market_cap_style_str = item.get('market_cap_style', '未知风格')

            # 格式化估值数据
            pe_str = f"{item.get('pe', 'N/A')}" if item.get('pe') is not None else "N/A"
            pe_ttm_str = f"{item.get('pe_ttm', 'N/A')}" if item.get('pe_ttm') is not None else "N/A"
            pb_str = f"{item.get('pb', 'N/A')}" if item.get('pb') is not None else "N/A"

            formatted_portfolio_items.append(
                f"- {item['symbol']} ({item.get('stock_name', '未知名称')} - {item.get('sw_l1_industry', '未知行业')}): "
                f"总市值: {total_mv_str}, 市值风格: {market_cap_style_str}, "
                f"PE: {pe_str}, PE(TTM): {pe_ttm_str}, PB: {pb_str}, "
                f"占净值比例 {item.get('stk_mkv_ratio', 'N/A')}%"
            )
        portfolio_data_str = "\n".join(formatted_portfolio_items)
        logger.info(f"基金 {fund_code} ({fund_name}) 持仓数据获取成功 (含股票名称、行业、市值和风格)。")

    # 3.2 获取申万行业分类信息
    logger.info(f"正在获取基金 {fund_code} 的申万行业分类信息...")
    sw_industry_info = get_fund_sw_industry_info(fund_code, report_end_date)

    sw_industry_info_str = "未获取到申万行业分类信息。"
    if sw_industry_info and sw_industry_info.get("holdings_sw_info"):
        formatted_sw_items = []
        for item in sw_industry_info["holdings_sw_info"]:
            formatted_sw_items.append(
                f"- {item['symbol']} ({item.get('stock_name', '未知名称')}): "
                f"一级行业: {item.get('l1_name', '未知')}, "
                f"二级行业: {item.get('l2_name', '未知')}, "
                f"三级行业: {item.get('l3_name', '未知')}, "
                f"占净值比例: {item.get('stk_mkv_ratio', 'N/A')}%"
            )
        sw_industry_info_str = "\n".join(formatted_sw_items)
        logger.info(f"基金 {fund_code} ({fund_name}) 申万行业分类信息获取成功。")
    else:
        logger.warning(f"基金 {fund_code} ({fund_name}) 未能获取申万行业分类信息。")

    # 3.2.2 获取港股申万行业分类信息
    logger.info(f"正在获取基金 {fund_code} 的港股申万行业分类信息...")
    hk_sw_industry_info = get_fund_hk_sw_industry_info(fund_code, report_end_date)

    hk_sw_industry_info_str = ""
    if hk_sw_industry_info and hk_sw_industry_info.get("holdings_hk_sw_info"):
        formatted_hk_sw_items = []
        for item in hk_sw_industry_info["holdings_hk_sw_info"]:
            formatted_hk_sw_items.append(
                f"- {item['symbol']} ({item.get('stock_name', '未知名称')}): "
                f"一级行业: {item.get('l1_name', '未知')}, "
                f"二级行业: {item.get('l2_name', '未知')}, "
                f"三级行业: {item.get('l3_name', '未知')}, "
                f"占净值比例: {item.get('stk_mkv_ratio', 'N/A')}%"
            )
        hk_sw_industry_info_str = "\n".join(formatted_hk_sw_items)
        logger.info(f"基金 {fund_code} ({fund_name}) 港股申万行业分类信息获取成功。")
    else:
        logger.info(f"基金 {fund_code} ({fund_name}) 未持有港股或未能获取港股申万行业分类信息。")

    # 合并A股和港股申万行业分类信息
    combined_sw_industry_info_str = sw_industry_info_str
    if hk_sw_industry_info_str:
        if combined_sw_industry_info_str:
            combined_sw_industry_info_str += "\n\n【港股申万行业分类】\n" + hk_sw_industry_info_str
        else:
            combined_sw_industry_info_str = "【港股申万行业分类】\n" + hk_sw_industry_info_str

    # 3.3 获取估值统计信息
    logger.info(f"正在获取基金 {fund_code} 的估值统计信息...")
    valuation_summary = get_fund_valuation_summary(fund_code, report_end_date)

    valuation_summary_str = "未获取到估值统计信息。"
    if valuation_summary:
        valuation_summary_str = f"""估值统计摘要：
- 持仓股票数量: {valuation_summary.get('total_stocks', 'N/A')}只
- 平均PE: {valuation_summary.get('avg_pe', 'N/A')}, 中位数PE: {valuation_summary.get('median_pe', 'N/A')}
- 平均PE(TTM): {valuation_summary.get('avg_pe_ttm', 'N/A')}, 中位数PE(TTM): {valuation_summary.get('median_pe_ttm', 'N/A')}
- 平均PB: {valuation_summary.get('avg_pb', 'N/A')}, 中位数PB: {valuation_summary.get('median_pb', 'N/A')}
- PE数据覆盖率: {valuation_summary.get('pe_coverage_ratio', 0)}%, PB数据覆盖率: {valuation_summary.get('pb_coverage_ratio', 0)}%"""
        logger.info(f"基金 {fund_code} ({fund_name}) 估值统计信息获取成功。")
    else:
        logger.warning(f"基金 {fund_code} ({fund_name}) 未能获取估值统计信息。")

    # 3.4 基金规模约束分析
    logger.info(f"正在进行基金 {fund_code} 的规模约束分析...")
    scale_analysis_str = "规模约束分析：数据不足，无法进行分析。"

    try:
        from modules.fund_scale_integration import FundScaleIntegration

        # 准备基金数据 - 转换为适合现有接口的格式
        if portfolio_data and isinstance(portfolio_data, list):
            # 将 portfolio_data 转换为字典格式，包含 top_holdings 字段
            portfolio_dict = {
                'top_holdings': []
            }

            for item in portfolio_data:
                # 提取市值信息（万元转亿元）
                total_mv_wanyuan = item.get('total_mv', 0)
                try:
                    market_cap_yi = float(total_mv_wanyuan) / 10000 if total_mv_wanyuan else 0
                except (ValueError, TypeError):
                    market_cap_yi = 0

                # 提取权重信息
                weight = item.get('stk_mkv_ratio', 0)
                try:
                    weight_decimal = float(weight) / 100 if weight else 0
                except (ValueError, TypeError):
                    weight_decimal = 0

                if market_cap_yi > 0 and weight_decimal > 0:
                    portfolio_dict['top_holdings'].append({
                        'stock_name': item.get('stock_name', item.get('symbol', '未知股票')),
                        'market_cap': market_cap_yi,
                        'market_cap_unit': '亿元',
                        'weight': weight_decimal
                    })

            # 进行规模约束分析
            integration = FundScaleIntegration()
            scale_analysis_result = integration.estimate_fund_scale_simple(portfolio_dict)

            if scale_analysis_result:
                scale_analysis_str = integration.get_scale_summary_for_llm(portfolio_dict)
                logger.info(f"基金 {fund_code} ({fund_name}) 规模约束分析完成。理论上限: {scale_analysis_result.get('fund_scale_limit_yi', 'N/A')}亿元")
            else:
                logger.warning(f"基金 {fund_code} ({fund_name}) 规模约束分析失败")
        else:
            logger.info(f"基金 {fund_code} ({fund_name}) 无有效持仓数据，跳过规模约束分析。")

    except Exception as e:
        logger.error(f"基金 {fund_code} ({fund_name}) 规模约束分析时发生错误: {e}", exc_info=True)

    # 4. LLM 分析执行 (IMP 3.4节)
    logger.info(f"正在使用LLM (模型: {model_name}) 分析基金 {fund_code}...")
    # 从配置中获取可选的 api_base 和流式响应配置
    api_base = os.getenv("OPENAI_API_BASE") # 或者您在.env中定义的其他变量名
    use_streaming = os.getenv("LLM_USE_STREAMING", "true").lower() == "true"
    logger.info(f"LLM配置: 流式响应={'启用' if use_streaming else '禁用'}")

    llm_report_content = analyze_with_llm(
        fund_code=fund_code,
        fund_name=fund_name,
        fund_manager=fund_manager,
        report_end_date=report_end_date,
        market_outlook=market_outlook,
        operation_analysis=operation_analysis,
        portfolio_data=portfolio_data_str, # 新增持仓数据参数
        sw_industry_info=combined_sw_industry_info_str, # 新增合并的申万行业分类信息参数
        valuation_summary=valuation_summary_str, # 新增估值统计信息参数
        scale_analysis=scale_analysis_str, # 新增规模约束分析参数
        analysis_rules=analysis_rules_content,
        model_name=model_name,
        api_key=api_key,
        api_base=api_base,
        use_streaming=use_streaming  # 新增流式响应配置
    )

    if llm_report_content is None:
        logger.error(f"LLM未能为基金 {fund_code} 生成报告。分析终止。")
        return

    logger.info(f"LLM为基金 {fund_code} 生成报告成功。")

    # 5.1 从LLM报告JSON中提取多维度信息并记录到知识图谱
    logger.info(f"正在从基金 {fund_code} 的LLM报告JSON中提取多维度分析信息...")
    try:
        # 尝试解析LLM报告中的JSON部分
        import json
        import re

        # 查找JSON部分（通常在```json和```之间）
        json_match = re.search(r'```json\s*(\{.*?\})\s*```', llm_report_content, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            report_json_data = json.loads(json_str)

            # 提取多维度信息
            extracted_data = {
                "concepts": {
                    "focus_exp_tag": report_json_data.get("V2_Summ_FocusExp_Derived"),
                    "manager_tags": report_json_data.get("Manager_Explicit_Concept_Tags_FromReport")
                },
                "manager_info": {
                    "name": report_json_data.get("ManagerName"),
                    "report_date": report_json_data.get("ReportDate")
                },
                "investment_strategy": {
                    "core_drivers": report_json_data.get("V2_FocusArea_CoreDrivers_Desc"),
                    "stock_picking": report_json_data.get("V2_FocusArea_StockPickingStrategy_Desc"),
                    "operation_features": report_json_data.get("V2_FocusArea_OperationFeatures_Desc"),
                    "derived_style": report_json_data.get("V2_FocusArea_DerivedStyleFeatures_Desc")
                },
                "portfolio_features": {
                    "concentration": report_json_data.get("V3_Portfolio_Concentration_Top10"),
                    "summary": report_json_data.get("V3_Portfolio_Summary_Detail")
                },
                "industry_info": {
                    "sw_l1": report_json_data.get("Upstream_SWS_L1_Name")
                },
                "insights": {
                    "positive": report_json_data.get("V5_Insight_Positive_Core"),
                    "risks": report_json_data.get("V5_Insight_Risk_Core")
                }
            }

            # 调用扩展的知识图谱记录函数
            from src.concept_extractor import record_and_save_enriched_analysis, record_concept_mentions
            record_and_save_enriched_analysis(extracted_data, fund_code)

            # 记录概念提及事件（用于基金经理追踪）
            record_concept_mentions(extracted_data, fund_code, fund_manager, report_json_data.get("ReportDate"))

            logger.info(f"基金 {fund_code}: 多维度分析信息已成功记录到知识图谱。")
        else:
            logger.warning(f"基金 {fund_code}: 未能在LLM报告中找到JSON部分，跳过信息提取。")
    except Exception as e:
        logger.error(f"基金 {fund_code}: 从LLM报告JSON提取多维度信息时发生错误: {e}", exc_info=True)
        # 尝试基本的聚合操作
        try:
            logger.warning(f"基金 {fund_code}: 尝试基本聚合操作...")
            if 'report_json_data' in locals() and report_json_data:
                basic_extracted_data = {
                    "manager_info": {
                        "name": report_json_data.get("ManagerName"),
                        "report_date": report_json_data.get("ReportDate")
                    }
                }
                from src.concept_extractor import record_and_save_enriched_analysis
                record_and_save_enriched_analysis(basic_extracted_data, fund_code)
                logger.info(f"基金 {fund_code}: 基本聚合操作成功")
        except Exception as fallback_e:
            logger.error(f"基金 {fund_code}: 基本聚合操作也失败: {fallback_e}", exc_info=True)

    # 在将 llm_report_content 传递给报告生成函数之前，直接替换占位符
    logger.info(f"准备替换报告内容中的占位符...")
    if fund_code and fund_name and fund_manager: #确保这些变量有值
        llm_report_content = llm_report_content.replace("[PLACEHOLDER_FUND_CODE]", fund_code)
        llm_report_content = llm_report_content.replace("[PLACEHOLDER_FUND_NAME]", fund_name)
        llm_report_content = llm_report_content.replace("[PLACEHOLDER_MANAGER_NAME]", fund_manager)
        # 添加ReportDate字段的替换
        if report_end_date and report_end_date != "未知报告日期":
            # 使用正则表达式精确替换ReportDate字段的值
            import re
            pattern = r'"ReportDate":\s*"[^"]*"'
            replacement = f'"ReportDate": "{report_end_date}"'
            llm_report_content = re.sub(pattern, replacement, llm_report_content)
            logger.info(f"报告内容中的占位符已替换，包括ReportDate: {report_end_date}")
        else:
            logger.warning(f"report_end_date 为空或未知，ReportDate字段将保持原值")

        # 添加AnalysisTimestamp字段的替换
        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
        analysis_pattern = r'"AnalysisTimestamp":\s*"[^"]*"'
        analysis_replacement = f'"AnalysisTimestamp": "{current_timestamp}"'
        llm_report_content = re.sub(analysis_pattern, analysis_replacement, llm_report_content)
        logger.info(f"报告内容中的占位符已替换，包括AnalysisTimestamp: {current_timestamp}")
    else:
        logger.warning(f"fund_code, fund_name, 或 fund_manager 为空，无法替换占位符。")


    # 5. 报告生成与输出 (IMP 3.5节)
    logger.info(f"正在对基金 {fund_code} 的LLM生成内容进行格式校验...")
    # 注意：validate_report_format 可能需要 analysis_rules_content 来校验章节标题
    # 如果 load_analysis_rules 返回空字符串，这里的校验可能不完整，但符合MVP策略
    # 移除 actual_report_date 的传递 (需要更新函数签名)
    is_format_valid = validate_report_format(llm_report_content, fund_code, fund_name, fund_manager, analysis_rules_content)
    if is_format_valid:
        logger.info(f"基金 {fund_code} 的报告内容基本格式校验通过。")
    else:
        logger.warning(f"基金 {fund_code} 的报告内容基本格式校验未通过，但仍将尝试保存。")

    logger.info(f"正在为基金 {fund_code} 保存Markdown报告...")
    # 移除 actual_report_date 的传递 (需要更新函数签名)
    md_file_path = save_report_md(llm_report_content, fund_code, fund_name, fund_manager, "reports")
    if md_file_path:
        logger.info(f"Markdown报告已成功保存至: {md_file_path}")
    else:
        logger.error(f"未能为基金 {fund_code} 保存Markdown报告。")

    logger.info(f"正在为基金 {fund_code} 生成并保存CSV报告...")
    # 移除 actual_report_date 的传递 (需要更新函数签名)
    csv_file_path = save_report_csv(llm_report_content, fund_code, fund_name, fund_manager, "reports")
    if csv_file_path:
        logger.info(f"CSV报告已成功保存至: {csv_file_path}")
    else:
        logger.warning(f"未能为基金 {fund_code} 生成或保存CSV报告 (可能是JSON提取问题或数据不适合CSV)。")

    logger.info(f"基金 {fund_code} 的分析和报告生成流程完成。")

def run_batch_analysis(fund_list_path: str = "data/fund_list.txt"):
    """
    执行批量基金分析流程。
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始批量基金分析，基金列表文件: {fund_list_path}")

    # 1. 加载配置 (与单个分析共享)
    load_dotenv()
    api_key = os.getenv("LLM_API_KEY")
    model_name = os.getenv("LLM_MODEL_NAME")
    api_base = os.getenv("OPENAI_API_BASE")
    use_streaming = os.getenv("LLM_USE_STREAMING", "true").lower() == "true"

    if not api_key or not model_name:
        logger.critical("LLM_API_KEY 或 LLM_MODEL_NAME 未在 .env 文件中正确配置。批量分析终止。")
        return

    # 2. 加载分析规则 (与单个分析共享)
    analysis_rules_content = load_analysis_rules()
    if not analysis_rules_content:
        logger.error("未能加载分析规则，批量分析终止。")
        return

    # 3. 读取基金列表
    fund_codes_to_process = read_fund_list_from_file(fund_list_path)
    if not fund_codes_to_process:
        logger.error(f"未能从 {fund_list_path} 读取基金代码列表或列表为空。批量分析终止。")
        return

    logger.info(f"将处理 {len(fund_codes_to_process)} 个基金: {fund_codes_to_process}")

    # 4. 批量获取数据
    # 注意: fetch_batch_fund_data 内部会调用 fetch_fund_data 和 fetch_fund_portfolio_data
    batch_data = fetch_batch_fund_data(fund_codes_to_process)

    successful_analyses = 0
    failed_analyses_data_fetch = 0
    failed_analyses_llm = 0
    failed_analyses_report = 0

    summary_details = []

    # 5. 循环处理每个基金
    for fund_code, data_result in batch_data.items():
        logger.info(f"--- 开始处理基金: {fund_code} ---")

        if data_result.get("error"):
            logger.error(f"基金 {fund_code}: 数据获取失败 - {data_result['error']}")
            summary_details.append(f"基金 {fund_code}: 数据获取失败 - {data_result['error']}")
            failed_analyses_data_fetch += 1
            continue

        quarterly_data = data_result.get("quarterly_data")
        portfolio_data_list = data_result.get("portfolio_data") # 这是列表形式

        if not quarterly_data: # fetch_batch_fund_data 应该已经标记错误，但再次检查
            logger.error(f"基金 {fund_code}: 季报数据缺失，跳过分析。")
            summary_details.append(f"基金 {fund_code}: 季报数据缺失，跳过分析。")
            failed_analyses_data_fetch += 1 # 归类为数据获取失败
            continue

        fund_name = quarterly_data.get("fund_name", "未知基金名称")
        fund_manager = quarterly_data.get("fund_manager", "未知基金经理")
        market_outlook = quarterly_data.get("market_outlook", "")
        operation_analysis = quarterly_data.get("operation_analysis", "")

        portfolio_data_str = "未获取到持仓数据或无持仓。"
        if portfolio_data_list:
            formatted_portfolio_items_batch = []
            for item in portfolio_data_list:
                total_mv_wanyuan_batch = item.get('total_mv')
                if total_mv_wanyuan_batch is not None:
                    try:
                        # 尝试将 total_mv_wanyuan_batch 转换为浮点数
                        total_mv_float_batch = float(total_mv_wanyuan_batch)
                        total_mv_yiyuan_batch = total_mv_float_batch / 10000
                        total_mv_str_batch = f"{total_mv_yiyuan_batch:.2f}亿元"
                    except (ValueError, TypeError):
                        logger.warning(f"批量处理中，股票 {item.get('symbol')} 的 total_mv ('{total_mv_wanyuan_batch}') 格式无效。")
                        total_mv_str_batch = "无效市值数据" # 或者 "未知市值"
                else:
                    total_mv_str_batch = "未知市值"

                market_cap_style_str_batch = item.get('market_cap_style', '未知风格')

                # 格式化估值数据
                pe_str_batch = f"{item.get('pe', 'N/A')}" if item.get('pe') is not None else "N/A"
                pe_ttm_str_batch = f"{item.get('pe_ttm', 'N/A')}" if item.get('pe_ttm') is not None else "N/A"
                pb_str_batch = f"{item.get('pb', 'N/A')}" if item.get('pb') is not None else "N/A"

                formatted_portfolio_items_batch.append(
                    f"- {item['symbol']} ({item.get('stock_name', '未知名称')} - {item.get('sw_l1_industry', '未知行业')}): "
                    f"总市值: {total_mv_str_batch}, 市值风格: {market_cap_style_str_batch}, "
                    f"PE: {pe_str_batch}, PE(TTM): {pe_ttm_str_batch}, PB: {pb_str_batch}, "
                    f"占净值比例 {item.get('stk_mkv_ratio', 'N/A')}%"
                )
            portfolio_data_str = "\n".join(formatted_portfolio_items_batch)
        elif quarterly_data.get("report_end_date") != "未知报告日期": # 有报告日期但无持仓
             portfolio_data_str = "报告期内无持仓数据或未披露。"

        # 获取申万行业分类信息
        batch_report_end_date = quarterly_data.get("report_end_date")
        sw_industry_info_str_batch = "未获取到申万行业分类信息。"
        if batch_report_end_date and batch_report_end_date != "未知报告日期":
            sw_industry_info_batch = get_fund_sw_industry_info(fund_code, batch_report_end_date)
            if sw_industry_info_batch and sw_industry_info_batch.get("holdings_sw_info"):
                formatted_sw_items_batch = []
                for item in sw_industry_info_batch["holdings_sw_info"]:
                    formatted_sw_items_batch.append(
                        f"- {item['symbol']} ({item.get('stock_name', '未知名称')}): "
                        f"一级行业: {item.get('l1_name', '未知')}, "
                        f"二级行业: {item.get('l2_name', '未知')}, "
                        f"三级行业: {item.get('l3_name', '未知')}, "
                        f"占净值比例: {item.get('stk_mkv_ratio', 'N/A')}%"
                    )
                sw_industry_info_str_batch = "\n".join(formatted_sw_items_batch)

        # 获取港股申万行业分类信息
        hk_sw_industry_info_str_batch = ""
        if batch_report_end_date and batch_report_end_date != "未知报告日期":
            hk_sw_industry_info_batch = get_fund_hk_sw_industry_info(fund_code, batch_report_end_date)
            if hk_sw_industry_info_batch and hk_sw_industry_info_batch.get("holdings_hk_sw_info"):
                formatted_hk_sw_items_batch = []
                for item in hk_sw_industry_info_batch["holdings_hk_sw_info"]:
                    formatted_hk_sw_items_batch.append(
                        f"- {item['symbol']} ({item.get('stock_name', '未知名称')}): "
                        f"一级行业: {item.get('l1_name', '未知')}, "
                        f"二级行业: {item.get('l2_name', '未知')}, "
                        f"三级行业: {item.get('l3_name', '未知')}, "
                        f"占净值比例: {item.get('stk_mkv_ratio', 'N/A')}%"
                    )
                hk_sw_industry_info_str_batch = "\n".join(formatted_hk_sw_items_batch)

        # 合并A股和港股申万行业分类信息
        combined_sw_industry_info_str_batch = sw_industry_info_str_batch
        if hk_sw_industry_info_str_batch:
            if combined_sw_industry_info_str_batch:
                combined_sw_industry_info_str_batch += "\n\n【港股申万行业分类】\n" + hk_sw_industry_info_str_batch
            else:
                combined_sw_industry_info_str_batch = "【港股申万行业分类】\n" + hk_sw_industry_info_str_batch

        # 获取估值统计信息（使用缓存的持仓数据）
        valuation_summary_str_batch = "未获取到估值统计信息。"
        if batch_report_end_date and batch_report_end_date != "未知报告日期":
            # 使用已获取的持仓数据，避免重复调用Wind API
            valuation_summary_batch = get_fund_valuation_summary(
                fund_code,
                batch_report_end_date,
                portfolio_data=portfolio_data_list
            )
            if valuation_summary_batch:
                valuation_summary_str_batch = f"""估值统计摘要：
- 持仓股票数量: {valuation_summary_batch.get('total_stocks', 'N/A')}只
- 平均PE: {valuation_summary_batch.get('avg_pe', 'N/A')}, 中位数PE: {valuation_summary_batch.get('median_pe', 'N/A')}
- 平均PE(TTM): {valuation_summary_batch.get('avg_pe_ttm', 'N/A')}, 中位数PE(TTM): {valuation_summary_batch.get('median_pe_ttm', 'N/A')}
- 平均PB: {valuation_summary_batch.get('avg_pb', 'N/A')}, 中位数PB: {valuation_summary_batch.get('median_pb', 'N/A')}
- PE数据覆盖率: {valuation_summary_batch.get('pe_coverage_ratio', 0)}%, PB数据覆盖率: {valuation_summary_batch.get('pb_coverage_ratio', 0)}%"""

        # 基金规模约束分析（批量分析）
        logger.info(f"正在进行基金 {fund_code} 的规模约束分析...")
        scale_analysis_str_batch = "规模约束分析：数据不足，无法进行分析。"

        try:
            from modules.fund_scale_integration import FundScaleIntegration

            # 准备基金数据 - 转换为适合现有接口的格式（批量分析）
            if portfolio_data_list and isinstance(portfolio_data_list, list):
                # 将 portfolio_data_list 转换为字典格式，包含 top_holdings 字段
                portfolio_dict_batch = {
                    'top_holdings': []
                }

                for item in portfolio_data_list:
                    # 提取市值信息（万元转亿元）
                    total_mv_wanyuan_batch = item.get('total_mv', 0)
                    try:
                        market_cap_yi_batch = float(total_mv_wanyuan_batch) / 10000 if total_mv_wanyuan_batch else 0
                    except (ValueError, TypeError):
                        market_cap_yi_batch = 0

                    # 提取权重信息
                    weight_batch = item.get('stk_mkv_ratio', 0)
                    try:
                        weight_decimal_batch = float(weight_batch) / 100 if weight_batch else 0
                    except (ValueError, TypeError):
                        weight_decimal_batch = 0

                    if market_cap_yi_batch > 0 and weight_decimal_batch > 0:
                        portfolio_dict_batch['top_holdings'].append({
                            'stock_name': item.get('stock_name', item.get('symbol', '未知股票')),
                            'market_cap': market_cap_yi_batch,
                            'market_cap_unit': '亿元',
                            'weight': weight_decimal_batch
                        })

                # 进行规模约束分析
                integration_batch = FundScaleIntegration()
                scale_analysis_result_batch = integration_batch.estimate_fund_scale_simple(portfolio_dict_batch)

                if scale_analysis_result_batch:
                    scale_analysis_str_batch = integration_batch.get_scale_summary_for_llm(portfolio_dict_batch)
                    logger.info(f"基金 {fund_code} ({fund_name}) 规模约束分析完成。理论上限: {scale_analysis_result_batch.get('fund_scale_limit_yi', 'N/A')}亿元")
                else:
                    logger.warning(f"基金 {fund_code} ({fund_name}) 规模约束分析失败")
            else:
                logger.info(f"基金 {fund_code} ({fund_name}) 无有效持仓数据，跳过规模约束分析。")

        except Exception as e:
            logger.error(f"基金 {fund_code} ({fund_name}) 规模约束分析时发生错误: {e}", exc_info=True)

        # LLM 分析
        logger.info(f"正在使用LLM (模型: {model_name}) 分析基金 {fund_code} ({fund_name})...")
        llm_report_content = analyze_with_llm(
            fund_code=fund_code,
            fund_name=fund_name,
            fund_manager=fund_manager,
            report_end_date=batch_report_end_date,
            market_outlook=market_outlook,
            operation_analysis=operation_analysis,
            portfolio_data=portfolio_data_str,
            sw_industry_info=combined_sw_industry_info_str_batch,
            valuation_summary=valuation_summary_str_batch,
            scale_analysis=scale_analysis_str_batch,  # 新增规模约束分析参数
            analysis_rules=analysis_rules_content,
            model_name=model_name,
            api_key=api_key,
            api_base=api_base,
            use_streaming=use_streaming  # 新增流式响应配置
        )

        if llm_report_content is None:
            logger.error(f"LLM未能为基金 {fund_code} 生成报告。")
            summary_details.append(f"基金 {fund_code} ({fund_name}): LLM分析失败。")
            failed_analyses_llm += 1
            continue

        logger.info(f"LLM为基金 {fund_code} 生成报告成功。")

        # 5.1 从LLM报告JSON中提取多维度信息并记录到知识图谱（批量分析）
        logger.info(f"正在从基金 {fund_code} 的LLM报告JSON中提取多维度分析信息...")

        # 先进行占位符替换，确保JSON解析时能获取到真实的基金经理信息
        if fund_code and fund_name and fund_manager:
            llm_report_content = llm_report_content.replace("[PLACEHOLDER_FUND_CODE]", fund_code)
            llm_report_content = llm_report_content.replace("[PLACEHOLDER_FUND_NAME]", fund_name)
            llm_report_content = llm_report_content.replace("[PLACEHOLDER_MANAGER_NAME]", fund_manager)
            logger.info(f"基金 {fund_code}: 已完成占位符替换，基金经理: {fund_manager}")

        try:
            # 尝试解析LLM报告中的JSON部分
            import json
            import re

            # 查找JSON部分（通常在```json和```之间）
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', llm_report_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)

                # 清理JSON格式错误
                # 修复属性名前的无效字符（如 - "属性名" -> "属性名"）
                json_str = re.sub(r'(\s+)-\s*("[\w\u4e00-\u9fff]+":)', r'\1\2', json_str)
                # 移除尾随逗号
                json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

                report_json_data = json.loads(json_str)

                # 提取多维度信息 - 使用中文字段名
                extracted_data = {
                    "concepts": {
                        "focus_exp_tag": report_json_data.get("聚焦暴露衍生标签"),
                        "manager_tags": report_json_data.get("基金经理明确概念标签")
                    },
                    "manager_info": {
                        "name": report_json_data.get("基金经理"),
                        "report_date": report_json_data.get("报告期")
                    },
                    "investment_strategy": {
                        "core_drivers": report_json_data.get("V2_FocusArea_CoreDrivers_Desc"),
                        "stock_picking": report_json_data.get("V2_FocusArea_StockPickingStrategy_Desc"),
                        "operation_features": report_json_data.get("V2_FocusArea_OperationFeatures_Desc"),
                        "derived_style": report_json_data.get("V2_FocusArea_DerivedStyleFeatures_Desc")
                    },
                    "portfolio_features": {
                        "concentration": report_json_data.get("V3_Portfolio_Concentration_Top10"),
                        "summary": report_json_data.get("V3_Portfolio_Summary_Detail")
                    },
                    "industry_info": {
                        "sw_l1": report_json_data.get("Upstream_SWS_L1_Name")
                    },
                    "insights": {
                        "positive": report_json_data.get("V5_Insight_Positive_Core"),
                        "risks": report_json_data.get("V5_Insight_Risk_Core")
                    }
                }

                # 调用扩展的知识图谱记录函数
                from src.concept_extractor import record_and_save_enriched_analysis, record_concept_mentions

                # 分别处理两个函数调用，避免异常相互影响
                try:
                    logger.info(f"基金 {fund_code}: 开始调用record_and_save_enriched_analysis...")
                    record_and_save_enriched_analysis(extracted_data, fund_code)
                    logger.info(f"基金 {fund_code}: record_and_save_enriched_analysis调用成功")
                except Exception as enriched_e:
                    logger.error(f"基金 {fund_code}: record_and_save_enriched_analysis调用失败: {enriched_e}", exc_info=True)

                # 记录概念提及事件（用于基金经理追踪）
                try:
                    logger.info(f"基金 {fund_code}: 开始调用record_concept_mentions...")
                    record_concept_mentions(extracted_data, fund_code, fund_manager, report_json_data.get("ReportDate"))
                    logger.info(f"基金 {fund_code}: record_concept_mentions调用成功")
                except Exception as mentions_e:
                    logger.error(f"基金 {fund_code}: record_concept_mentions调用失败: {mentions_e}", exc_info=True)

                logger.info(f"基金 {fund_code}: 多维度分析信息处理完成。")
            else:
                logger.warning(f"基金 {fund_code}: 未能在LLM报告中找到JSON部分，跳过信息提取。")
        except json.JSONDecodeError as e:
            logger.error(f"基金 {fund_code}: JSON解析错误: {e}")
            logger.error(f"错误位置: 第{e.lineno}行, 第{e.colno}列")
            if 'json_str' in locals():
                # 显示错误附近的内容
                lines = json_str.split('\n')
                start_line = max(0, e.lineno - 3)
                end_line = min(len(lines), e.lineno + 2)
                logger.error("错误附近的JSON内容:")
                for i in range(start_line, end_line):
                    line_num = i + 1
                    marker = " >>> " if line_num == e.lineno else "     "
                    logger.error(f"{marker}第{line_num:2d}行: {lines[i]}")
        except Exception as e:
            logger.error(f"基金 {fund_code}: 从LLM报告JSON提取多维度信息时发生错误: {e}", exc_info=True)
            # 尝试基本的聚合操作
            try:
                logger.warning(f"基金 {fund_code}: 尝试基本聚合操作...")
                if 'report_json_data' in locals() and report_json_data:
                    basic_extracted_data = {
                        "manager_info": {
                            "name": report_json_data.get("ManagerName"),
                            "report_date": report_json_data.get("ReportDate")
                        }
                    }
                    from src.concept_extractor import record_and_save_enriched_analysis
                    record_and_save_enriched_analysis(basic_extracted_data, fund_code)
                    logger.info(f"基金 {fund_code}: 基本聚合操作成功")
            except Exception as fallback_e:
                logger.error(f"基金 {fund_code}: 基本聚合操作也失败: {fallback_e}", exc_info=True)

        # 添加ReportDate和AnalysisTimestamp字段的替换
        batch_report_end_date = quarterly_data.get("report_end_date")
        if batch_report_end_date and batch_report_end_date != "未知报告日期":
            # 使用正则表达式精确替换ReportDate字段的值
            import re
            pattern = r'"ReportDate":\s*"[^"]*"'
            replacement = f'"ReportDate": "{batch_report_end_date}"'
            llm_report_content = re.sub(pattern, replacement, llm_report_content)
            logger.info(f"基金 {fund_code} 报告内容中的占位符已替换，包括ReportDate: {batch_report_end_date}")
        else:
            logger.warning(f"基金 {fund_code} 的 report_end_date 为空或未知，ReportDate字段将保持原值")

        # 添加AnalysisTimestamp字段的替换
        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
        analysis_pattern = r'"AnalysisTimestamp":\s*"[^"]*"'
        analysis_replacement = f'"AnalysisTimestamp": "{current_timestamp}"'
        llm_report_content = re.sub(analysis_pattern, analysis_replacement, llm_report_content)
        logger.info(f"基金 {fund_code} 报告内容中的占位符已替换，包括AnalysisTimestamp: {current_timestamp}")

        # 报告生成与校验
        report_saved_md = False
        report_saved_csv = False

        is_format_valid = validate_report_format(llm_report_content, fund_code, fund_name, fund_manager, analysis_rules_content)
        if is_format_valid:
            logger.info(f"基金 {fund_code} 的报告内容基本格式校验通过。")
        else:
            logger.warning(f"基金 {fund_code} 的报告内容基本格式校验未通过，但仍将尝试保存。")

        md_file_path = save_report_md(llm_report_content, fund_code, fund_name, fund_manager, "reports")
        if md_file_path:
            logger.info(f"Markdown报告已成功保存至: {md_file_path}")
            report_saved_md = True
        else:
            logger.error(f"未能为基金 {fund_code} 保存Markdown报告。")

        csv_file_path = save_report_csv(llm_report_content, fund_code, fund_name, fund_manager, "reports")
        if csv_file_path:
            logger.info(f"CSV报告已成功保存至: {csv_file_path}")
            report_saved_csv = True
        else:
            logger.warning(f"未能为基金 {fund_code} 生成或保存CSV报告。")

        if report_saved_md: # 主要以MD报告为准
            successful_analyses += 1
            summary_details.append(f"基金 {fund_code} ({fund_name}): 分析成功，报告已保存 ({md_file_path}{', ' + csv_file_path if report_saved_csv else ''})。")
        else:
            failed_analyses_report +=1
            summary_details.append(f"基金 {fund_code} ({fund_name}): 报告保存失败。")

        logger.info(f"基金 {fund_code} 处理完成。")

    # 6. 生成执行摘要
    logger.info("--- 批量分析执行摘要 ---")
    total_funds = len(fund_codes_to_process)
    logger.info(f"总计基金数量: {total_funds}")
    logger.info(f"成功分析数量: {successful_analyses}")
    logger.info(f"数据获取失败数量: {failed_analyses_data_fetch}")
    logger.info(f"LLM分析失败数量: {failed_analyses_llm}")
    logger.info(f"报告保存失败数量: {failed_analyses_report}")

    summary_content = [
        f"批量分析执行摘要 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        f"总计基金数量: {total_funds}",
        f"成功分析数量: {successful_analyses}",
        f"数据获取失败数量: {failed_analyses_data_fetch}",
        f"LLM分析失败数量: {failed_analyses_llm}",
        f"报告保存失败数量: {failed_analyses_report}",
        "\n详细情况:"
    ]
    summary_content.extend(summary_details)

    summary_filename = f"batch_summary_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    summary_filepath = os.path.join("reports", summary_filename) # 假设摘要也保存在reports目录

    try:
        # 确保 reports 目录存在
        os.makedirs("reports", exist_ok=True)
        with open(summary_filepath, "w", encoding="utf-8") as f:
            f.write("\n".join(summary_content))
        logger.info(f"执行摘要已保存至: {summary_filepath}")
    except Exception as e:
        logger.error(f"保存执行摘要失败: {e}", exc_info=True)

    logger.info("批量基金分析流程结束。")


def run_batch_analysis_parallel(fund_list_path: str = "data/fund_list.txt", max_workers: int = 2, enable_parallel: bool = True):
    """
    执行批量基金分析流程（并行版本）

    Args:
        fund_list_path: 基金列表文件路径
        max_workers: 最大并发数（建议2-3个）
        enable_parallel: 是否启用并行（False时退回串行模式）

    注意：这是原有run_batch_analysis函数的并行优化版本，保持完全向后兼容
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始批量基金分析（并行版本），基金列表文件: {fund_list_path}")
    logger.info(f"并行设置: 启用={enable_parallel}, 最大并发数={max_workers}")

    # 1. 加载配置 (与原版本相同)
    load_dotenv()
    api_key = os.getenv("LLM_API_KEY")
    model_name = os.getenv("LLM_MODEL_NAME")
    api_base = os.getenv("OPENAI_API_BASE")
    use_streaming = os.getenv("LLM_USE_STREAMING", "true").lower() == "true"

    if not api_key or not model_name:
        logger.critical("LLM_API_KEY 或 LLM_MODEL_NAME 未在 .env 文件中正确配置。批量分析终止。")
        return

    # 2. 加载分析规则 (与原版本相同)
    analysis_rules_content = load_analysis_rules()
    if not analysis_rules_content:
        logger.error("未能加载分析规则，批量分析终止。")
        return

    # 3. 读取基金列表 (与原版本相同)
    fund_codes_to_process = read_fund_list_from_file(fund_list_path)
    if not fund_codes_to_process:
        logger.error(f"未能从 {fund_list_path} 读取基金代码列表或列表为空。批量分析终止。")
        return

    logger.info(f"将处理 {len(fund_codes_to_process)} 个基金: {fund_codes_to_process}")

    # 4. 批量获取数据 (与原版本相同)
    batch_data = fetch_batch_fund_data(fund_codes_to_process)

    # 5. 准备LLM分析任务
    analysis_tasks = []
    fund_data_map = {}  # 用于存储基金数据，便于后续处理

    for fund_code, data_result in batch_data.items():
        if data_result.get("error"):
            logger.error(f"基金 {fund_code}: 数据获取失败 - {data_result['error']}")
            continue

        quarterly_data = data_result.get("quarterly_data")
        portfolio_data_list = data_result.get("portfolio_data")

        if not quarterly_data:
            logger.error(f"基金 {fund_code}: 季报数据缺失，跳过分析。")
            continue

        # 准备分析参数（与原版本逻辑相同）
        try:
            fund_name = quarterly_data.get("fund_name", "未知基金名称")
            fund_manager = quarterly_data.get("fund_manager", "未知基金经理")
            batch_report_end_date = quarterly_data.get("report_end_date", "未知报告日期")
            market_outlook = quarterly_data.get("market_outlook", "")
            operation_analysis = quarterly_data.get("operation_analysis", "")

            # 处理持仓数据
            portfolio_data_str = ""
            if portfolio_data_list and len(portfolio_data_list) > 0:
                portfolio_data_str = "\n".join([
                    f"股票代码: {item.get('stock_code', 'N/A')}, "
                    f"股票名称: {item.get('stock_name', 'N/A')}, "
                    f"持仓比例: {item.get('holding_ratio', 'N/A')}%, "
                    f"持股数量: {item.get('holding_shares', 'N/A')}万股, "
                    f"持股市值: {item.get('holding_market_value', 'N/A')}万元"
                    for item in portfolio_data_list
                ])
            else:
                portfolio_data_str = "无持仓数据"

            # 获取申万行业分类信息（与串行版本完全一致）
            # 获取A股申万行业分类信息
            sw_industry_info_str_batch = ""
            if batch_report_end_date and batch_report_end_date != "未知报告日期":
                sw_industry_info_batch = get_fund_sw_industry_info(fund_code, batch_report_end_date)
                if sw_industry_info_batch and sw_industry_info_batch.get("holdings_sw_info"):
                    formatted_sw_items_batch = []
                    for item in sw_industry_info_batch["holdings_sw_info"]:
                        formatted_sw_items_batch.append(
                            f"- {item['symbol']} ({item.get('stock_name', '未知名称')}): "
                            f"一级行业: {item.get('l1_name', '未知')}, "
                            f"二级行业: {item.get('l2_name', '未知')}, "
                            f"三级行业: {item.get('l3_name', '未知')}, "
                            f"占净值比例: {item.get('stk_mkv_ratio', 'N/A')}%"
                        )
                    sw_industry_info_str_batch = "\n".join(formatted_sw_items_batch)

            # 获取港股申万行业分类信息
            hk_sw_industry_info_str_batch = ""
            if batch_report_end_date and batch_report_end_date != "未知报告日期":
                hk_sw_industry_info_batch = get_fund_hk_sw_industry_info(fund_code, batch_report_end_date)
                if hk_sw_industry_info_batch and hk_sw_industry_info_batch.get("holdings_hk_sw_info"):
                    formatted_hk_sw_items_batch = []
                    for item in hk_sw_industry_info_batch["holdings_hk_sw_info"]:
                        formatted_hk_sw_items_batch.append(
                            f"- {item['symbol']} ({item.get('stock_name', '未知名称')}): "
                            f"一级行业: {item.get('l1_name', '未知')}, "
                            f"二级行业: {item.get('l2_name', '未知')}, "
                            f"三级行业: {item.get('l3_name', '未知')}, "
                            f"占净值比例: {item.get('stk_mkv_ratio', 'N/A')}%"
                        )
                    hk_sw_industry_info_str_batch = "\n".join(formatted_hk_sw_items_batch)

            # 合并A股和港股申万行业分类信息
            combined_sw_industry_info_str_batch = sw_industry_info_str_batch
            if hk_sw_industry_info_str_batch:
                if combined_sw_industry_info_str_batch:
                    combined_sw_industry_info_str_batch += "\n\n【港股申万行业分类】\n" + hk_sw_industry_info_str_batch
                else:
                    combined_sw_industry_info_str_batch = "【港股申万行业分类】\n" + hk_sw_industry_info_str_batch

            # 获取估值统计信息（使用缓存的持仓数据）
            valuation_summary_str_batch = "未获取到估值统计信息。"
            if batch_report_end_date and batch_report_end_date != "未知报告日期":
                # 使用已获取的持仓数据，避免重复调用Wind API
                valuation_summary_batch = get_fund_valuation_summary(
                    fund_code,
                    batch_report_end_date,
                    portfolio_data=portfolio_data_list
                )
                if valuation_summary_batch:
                    valuation_summary_str_batch = f"""估值统计摘要：
- 持仓股票数量: {valuation_summary_batch.get('total_stocks', 'N/A')}只
- 平均PE: {valuation_summary_batch.get('avg_pe', 'N/A')}, 中位数PE: {valuation_summary_batch.get('median_pe', 'N/A')}
- 平均PE(TTM): {valuation_summary_batch.get('avg_pe_ttm', 'N/A')}, 中位数PE(TTM): {valuation_summary_batch.get('median_pe_ttm', 'N/A')}
- 平均PB: {valuation_summary_batch.get('avg_pb', 'N/A')}, 中位数PB: {valuation_summary_batch.get('median_pb', 'N/A')}
- PE数据覆盖率: {valuation_summary_batch.get('pe_coverage_ratio', 0)}%, PB数据覆盖率: {valuation_summary_batch.get('pb_coverage_ratio', 0)}%"""

            # 获取规模约束分析（与串行版本完全一致）
            logger.info(f"正在进行基金 {fund_code} 的规模约束分析...")
            scale_analysis_str_batch = "规模约束分析：数据不足，无法进行分析。"

            try:
                from modules.fund_scale_integration import FundScaleIntegration

                # 准备基金数据 - 转换为适合现有接口的格式（批量分析）
                if portfolio_data_list and isinstance(portfolio_data_list, list):
                    # 将 portfolio_data_list 转换为字典格式，包含 top_holdings 字段
                    portfolio_dict_batch = {
                        'top_holdings': []
                    }

                    for item in portfolio_data_list:
                        # 提取市值信息（万元转亿元）
                        total_mv_wanyuan_batch = item.get('total_mv', 0)
                        try:
                            market_cap_yi_batch = float(total_mv_wanyuan_batch) / 10000 if total_mv_wanyuan_batch else 0
                        except (ValueError, TypeError):
                            market_cap_yi_batch = 0

                        # 提取权重信息
                        weight_batch = item.get('stk_mkv_ratio', 0)
                        try:
                            weight_decimal_batch = float(weight_batch) / 100 if weight_batch else 0
                        except (ValueError, TypeError):
                            weight_decimal_batch = 0

                        if market_cap_yi_batch > 0 and weight_decimal_batch > 0:
                            portfolio_dict_batch['top_holdings'].append({
                                'stock_name': item.get('stock_name', item.get('symbol', '未知股票')),
                                'market_cap': market_cap_yi_batch,
                                'market_cap_unit': '亿元',
                                'weight': weight_decimal_batch
                            })

                    # 进行规模约束分析
                    integration_batch = FundScaleIntegration()
                    scale_analysis_result_batch = integration_batch.estimate_fund_scale_simple(portfolio_dict_batch)

                    if scale_analysis_result_batch:
                        scale_analysis_str_batch = integration_batch.get_scale_summary_for_llm(portfolio_dict_batch)
                        logger.info(f"基金 {fund_code} ({fund_name}) 规模约束分析完成。理论上限: {scale_analysis_result_batch.get('fund_scale_limit_yi', 'N/A')}亿元")
                    else:
                        logger.warning(f"基金 {fund_code} ({fund_name}) 规模约束分析失败")
                else:
                    logger.info(f"基金 {fund_code} ({fund_name}) 无有效持仓数据，跳过规模约束分析。")

            except Exception as e:
                logger.error(f"基金 {fund_code} ({fund_name}) 规模约束分析时发生错误: {e}", exc_info=True)

            # 创建分析任务（使用与串行版本相同的关键字参数调用方式）
            task = create_analysis_task(
                analyze_with_llm,
                fund_code=fund_code,
                fund_name=fund_name,
                fund_manager=fund_manager,
                report_end_date=batch_report_end_date,
                market_outlook=market_outlook,
                operation_analysis=operation_analysis,
                portfolio_data=portfolio_data_str,
                sw_industry_info=combined_sw_industry_info_str_batch,
                valuation_summary=valuation_summary_str_batch,
                scale_analysis=scale_analysis_str_batch,
                analysis_rules=analysis_rules_content,
                model_name=model_name,
                api_key=api_key,
                api_base=api_base,
                use_streaming=use_streaming  # 新增流式响应配置
            )

            analysis_tasks.append(task)

            # 保存基金数据用于后续处理
            fund_data_map[fund_code] = {
                'quarterly_data': quarterly_data,
                'portfolio_data_list': portfolio_data_list,
                'fund_name': fund_name,
                'fund_manager': fund_manager,
                'batch_report_end_date': batch_report_end_date
            }

        except Exception as e:
            logger.error(f"基金 {fund_code}: 准备分析任务时发生错误: {e}", exc_info=True)
            continue

    if not analysis_tasks:
        logger.error("没有有效的分析任务，批量分析终止。")
        return

    # 6. 执行并行LLM分析
    logger.info(f"开始执行 {len(analysis_tasks)} 个LLM分析任务...")

    processor = SimpleLLMParallelProcessor(
        max_workers=max_workers,
        enable_parallel=enable_parallel,
        request_interval=3.0  # 3秒请求间隔，避免503错误
    )

    try:
        analysis_results = processor.process_batch_funds(analysis_tasks)
    finally:
        processor.close()

    # 7. 处理分析结果（与原版本逻辑相同）
    successful_analyses = 0
    failed_analyses_data_fetch = 0
    failed_analyses_llm = 0
    failed_analyses_report = 0
    summary_details = []

    for result in analysis_results:
        fund_code = result['fund_code']

        if not result['success']:
            logger.error(f"基金 {fund_code}: LLM分析失败 - {result['error']}")
            summary_details.append(f"基金 {fund_code}: LLM分析失败 - {result['error']}")
            failed_analyses_llm += 1
            continue

        llm_report_content = result['result']
        if not llm_report_content:
            logger.error(f"基金 {fund_code}: LLM返回空结果")
            summary_details.append(f"基金 {fund_code}: LLM返回空结果")
            failed_analyses_llm += 1
            continue

        # 获取基金数据
        fund_data = fund_data_map.get(fund_code)
        if not fund_data:
            logger.error(f"基金 {fund_code}: 找不到对应的基金数据")
            failed_analyses_report += 1
            continue

        fund_name = fund_data['fund_name']
        fund_manager = fund_data['fund_manager']
        batch_report_end_date = fund_data['batch_report_end_date']

        logger.info(f"基金 {fund_code} LLM分析完成，耗时: {result['duration']:.1f}秒")

        # 后续处理逻辑（与串行版本完全一致）
        try:
            # 5.1 从LLM报告JSON中提取多维度信息并记录到知识图谱（批量分析）
            logger.info(f"正在从基金 {fund_code} 的LLM报告JSON中提取多维度分析信息...")

            # 先进行占位符替换，确保JSON解析时能获取到真实的基金经理信息
            if fund_code and fund_name and fund_manager:
                llm_report_content = llm_report_content.replace("[PLACEHOLDER_FUND_CODE]", fund_code)
                llm_report_content = llm_report_content.replace("[PLACEHOLDER_FUND_NAME]", fund_name)
                llm_report_content = llm_report_content.replace("[PLACEHOLDER_MANAGER_NAME]", fund_manager)
                logger.info(f"基金 {fund_code}: 已完成占位符替换，基金经理: {fund_manager}")

            try:
                # 尝试解析LLM报告中的JSON部分
                import json
                import re

                # 查找JSON部分（通常在```json和```之间）
                json_match = re.search(r'```json\s*(\{.*?\})\s*```', llm_report_content, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    report_json_data = json.loads(json_str)

                    # 提取多维度信息 - 使用中文字段名
                    extracted_data = {
                        "concepts": {
                            "focus_exp_tag": report_json_data.get("聚焦暴露衍生标签"),
                            "manager_tags": report_json_data.get("基金经理明确概念标签")
                        },
                        "manager_info": {
                            "name": report_json_data.get("基金经理"),
                            "report_date": report_json_data.get("报告期")
                        },
                        "investment_strategy": {
                            "core_drivers": report_json_data.get("V2_FocusArea_CoreDrivers_Desc"),
                            "stock_picking": report_json_data.get("V2_FocusArea_StockPickingStrategy_Desc"),
                            "operation_features": report_json_data.get("V2_FocusArea_OperationFeatures_Desc"),
                            "derived_style": report_json_data.get("V2_FocusArea_DerivedStyleFeatures_Desc")
                        },
                        "portfolio_features": {
                            "concentration": report_json_data.get("V3_Portfolio_Concentration_Top10"),
                            "summary": report_json_data.get("V3_Portfolio_Summary_Detail")
                        },
                        "industry_info": {
                            "sw_l1": report_json_data.get("Upstream_SWS_L1_Name")
                        },
                        "insights": {
                            "positive": report_json_data.get("V5_Insight_Positive_Core"),
                            "risks": report_json_data.get("V5_Insight_Risk_Core")
                        }
                    }

                    # 调用扩展的知识图谱记录函数
                    from src.concept_extractor import record_and_save_enriched_analysis, record_concept_mentions

                    # 分别处理两个函数调用，避免异常相互影响
                    try:
                        logger.info(f"基金 {fund_code}: 开始调用record_and_save_enriched_analysis...")
                        record_and_save_enriched_analysis(extracted_data, fund_code)
                        logger.info(f"基金 {fund_code}: record_and_save_enriched_analysis调用成功")
                    except Exception as enriched_e:
                        logger.error(f"基金 {fund_code}: record_and_save_enriched_analysis调用失败: {enriched_e}", exc_info=True)

                    # 记录概念提及事件（用于基金经理追踪）
                    try:
                        logger.info(f"基金 {fund_code}: 开始调用record_concept_mentions...")
                        record_concept_mentions(extracted_data, fund_code, fund_manager, report_json_data.get("ReportDate"))
                        logger.info(f"基金 {fund_code}: record_concept_mentions调用成功")
                    except Exception as mentions_e:
                        logger.error(f"基金 {fund_code}: record_concept_mentions调用失败: {mentions_e}", exc_info=True)

                    logger.info(f"基金 {fund_code}: 多维度分析信息处理完成。")
                else:
                    logger.warning(f"基金 {fund_code}: 未能在LLM报告中找到JSON部分，跳过信息提取。")
            except Exception as e:
                logger.error(f"基金 {fund_code}: 从LLM报告JSON提取多维度信息时发生错误: {e}", exc_info=True)
                # 尝试基本的聚合操作
                try:
                    logger.warning(f"基金 {fund_code}: 尝试基本聚合操作...")
                    if 'report_json_data' in locals() and report_json_data:
                        basic_extracted_data = {
                            "manager_info": {
                                "name": report_json_data.get("ManagerName"),
                                "report_date": report_json_data.get("ReportDate")
                            }
                        }
                        from src.concept_extractor import record_and_save_enriched_analysis
                        record_and_save_enriched_analysis(basic_extracted_data, fund_code)
                        logger.info(f"基金 {fund_code}: 基本聚合操作成功")
                except Exception as fallback_e:
                    logger.error(f"基金 {fund_code}: 基本聚合操作也失败: {fallback_e}", exc_info=True)

            # 添加ReportDate和AnalysisTimestamp字段的替换
            quarterly_data = fund_data['quarterly_data']
            batch_report_end_date = quarterly_data.get("report_end_date")
            if batch_report_end_date and batch_report_end_date != "未知报告日期":
                # 使用正则表达式精确替换ReportDate字段的值
                import re
                pattern = r'"ReportDate":\s*"[^"]*"'
                replacement = f'"ReportDate": "{batch_report_end_date}"'
                llm_report_content = re.sub(pattern, replacement, llm_report_content)
                logger.info(f"基金 {fund_code} 报告内容中的占位符已替换，包括ReportDate: {batch_report_end_date}")
            else:
                logger.warning(f"基金 {fund_code} 的 report_end_date 为空或未知，ReportDate字段将保持原值")

            # 添加AnalysisTimestamp字段的替换
            current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
            analysis_pattern = r'"AnalysisTimestamp":\s*"[^"]*"'
            analysis_replacement = f'"AnalysisTimestamp": "{current_timestamp}"'
            llm_report_content = re.sub(analysis_pattern, analysis_replacement, llm_report_content)
            logger.info(f"基金 {fund_code} 报告内容中的占位符已替换，包括AnalysisTimestamp: {current_timestamp}")

            # 报告生成与校验
            report_saved_md = False
            report_saved_csv = False

            is_format_valid = validate_report_format(llm_report_content, fund_code, fund_name, fund_manager, analysis_rules_content)
            if is_format_valid:
                logger.info(f"基金 {fund_code} 的报告内容基本格式校验通过。")
            else:
                logger.warning(f"基金 {fund_code} 的报告内容基本格式校验未通过，但仍将尝试保存。")

            md_file_path = save_report_md(llm_report_content, fund_code, fund_name, fund_manager, "reports")
            if md_file_path:
                logger.info(f"Markdown报告已成功保存至: {md_file_path}")
                report_saved_md = True
            else:
                logger.error(f"未能为基金 {fund_code} 保存Markdown报告。")

            csv_file_path = save_report_csv(llm_report_content, fund_code, fund_name, fund_manager, "reports")
            if csv_file_path:
                logger.info(f"CSV报告已成功保存至: {csv_file_path}")
                report_saved_csv = True
            else:
                logger.warning(f"未能为基金 {fund_code} 生成或保存CSV报告。")

            if report_saved_md: # 主要以MD报告为准
                successful_analyses += 1
                summary_details.append(f"基金 {fund_code} ({fund_name}): 分析成功，报告已保存 ({md_file_path}{', ' + csv_file_path if report_saved_csv else ''})。")
            else:
                failed_analyses_report +=1
                summary_details.append(f"基金 {fund_code} ({fund_name}): 报告保存失败。")

            logger.info(f"基金 {fund_code} 处理完成。")

        except Exception as e:
            logger.error(f"基金 {fund_code}: 后续处理失败: {e}", exc_info=True)
            failed_analyses_report += 1
            summary_details.append(f"基金 {fund_code}: 后续处理失败 - {str(e)}")

    # 8. 生成执行摘要（与原版本相同）
    logger.info("--- 批量分析执行摘要（并行版本） ---")
    total_funds = len(fund_codes_to_process)
    logger.info(f"总计基金数量: {total_funds}")
    logger.info(f"成功分析数量: {successful_analyses}")
    logger.info(f"数据获取失败数量: {failed_analyses_data_fetch}")
    logger.info(f"LLM分析失败数量: {failed_analyses_llm}")
    logger.info(f"报告保存失败数量: {failed_analyses_report}")

    summary_content = [
        f"批量分析执行摘要（并行版本） - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        f"并行设置: 启用={enable_parallel}, 最大并发数={max_workers}",
        f"总计基金数量: {total_funds}",
        f"成功分析数量: {successful_analyses}",
        f"数据获取失败数量: {failed_analyses_data_fetch}",
        f"LLM分析失败数量: {failed_analyses_llm}",
        f"报告保存失败数量: {failed_analyses_report}",
        "\n详细情况:"
    ]
    summary_content.extend(summary_details)

    summary_filename = f"batch_summary_parallel_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    summary_filepath = os.path.join("reports", summary_filename)

    try:
        os.makedirs("reports", exist_ok=True)
        with open(summary_filepath, "w", encoding="utf-8") as f:
            f.write("\n".join(summary_content))
        logger.info(f"执行摘要已保存至: {summary_filepath}")
    except Exception as e:
        logger.error(f"保存执行摘要失败: {e}", exc_info=True)

    logger.info("批量基金分析流程结束（并行版本）。")


def parse_command_line_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='基金季报分析系统')

    # 基本参数
    parser.add_argument('--fund-code', type=str, help='分析单个基金代码')
    parser.add_argument('--parallel', action='store_true', help='启用并行处理')
    parser.add_argument('--max-workers', type=int, help='最大并行工作线程数')

    return parser.parse_args()


if __name__ == "__main__":
    setup_logging()
    logger = logging.getLogger(__name__) # 在 setup_logging 后获取 logger

    try:
        # 解析命令行参数
        args = parse_command_line_args()

        # 加载并行配置
        parallel_config = get_parallel_config()
        current_settings = parallel_config.get_current_settings()

        # 命令行参数覆盖并行配置
        if args.parallel:
            current_settings['enabled'] = True
        if args.max_workers:
            current_settings['max_workers'] = args.max_workers

        logger.info(f"当前并行配置: {current_settings}")

        # 执行分析
        if args.fund_code:
            # 单个基金分析
            logger.info(f"执行单个基金分析: {args.fund_code}")
            run_analysis(args.fund_code)
        else:
            # 批量分析
            if current_settings['enabled']:
                logger.info(f"程序启动，准备执行批量分析（并行模式，最大并发数: {current_settings['max_workers']}）...")
                run_batch_analysis_parallel(
                    max_workers=current_settings['max_workers'],
                    enable_parallel=True
                )
            else:
                logger.info("程序启动，准备执行批量分析（串行模式）...")
                run_batch_analysis() # 默认使用 data/fund_list.txt

        # 以下是原有的单个基金分析逻辑，可以按需保留或通过参数启用
        # logger.info("--- 单个基金分析 (可选) ---")
        # fund_code_input_single = input("请输入要分析的单个基金代码 (例如 005682.OF，或输入 'skip' 跳过): ")
        # if fund_code_input_single and fund_code_input_single.lower() != 'skip':
        #     logger.info(f"执行单个基金分析: {fund_code_input_single.strip()}")
        #     run_analysis(fund_code_input_single.strip())
        # elif fund_code_input_single.lower() == 'skip':
        #     logger.info("已跳过单个基金分析。")
        # else:
        #     logger.info("未输入单个基金代码，不执行单个分析。")

    except Exception as e:
        logger.critical(f"主程序发生未捕获的意外错误: {e}", exc_info=True)