#!/usr/bin/env python3
"""
分析基金代码后缀不匹配问题的规模和影响
"""

import logging
import os
from dotenv import load_dotenv
from sqlalchemy import create_engine, text

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def analyze_fund_code_mismatch():
    """分析基金代码后缀不匹配问题"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    # 构建数据库连接字符串
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            logger.info("=== 基金代码后缀不匹配问题深度分析 ===\n")
            
            # 1. 统计各表中的基金代码后缀分布
            logger.info("1. 基金代码后缀分布分析")
            logger.info("-" * 50)
            
            # fund_quarterly_data表的后缀分布
            quarterly_suffix_query = text("""
                SELECT 
                    CASE 
                        WHEN fund_code LIKE '%.OF' THEN '.OF'
                        WHEN fund_code LIKE '%.SZ' THEN '.SZ'
                        WHEN fund_code LIKE '%.SH' THEN '.SH'
                        ELSE 'OTHER'
                    END as suffix,
                    COUNT(DISTINCT fund_code) as fund_count,
                    COUNT(*) as record_count
                FROM fund_quarterly_data
                GROUP BY 
                    CASE 
                        WHEN fund_code LIKE '%.OF' THEN '.OF'
                        WHEN fund_code LIKE '%.SZ' THEN '.SZ'
                        WHEN fund_code LIKE '%.SH' THEN '.SH'
                        ELSE 'OTHER'
                    END
                ORDER BY fund_count DESC
            """)
            
            result = conn.execute(quarterly_suffix_query)
            quarterly_suffixes = result.fetchall()
            
            logger.info("fund_quarterly_data表后缀分布:")
            for suffix_info in quarterly_suffixes:
                logger.info(f"  {suffix_info[0]}: {suffix_info[1]} 个基金, {suffix_info[2]} 条记录")
            
            # tushare_fund_portfolio表的后缀分布
            portfolio_suffix_query = text("""
                SELECT 
                    CASE 
                        WHEN ts_code LIKE '%.OF' THEN '.OF'
                        WHEN ts_code LIKE '%.SZ' THEN '.SZ'
                        WHEN ts_code LIKE '%.SH' THEN '.SH'
                        ELSE 'OTHER'
                    END as suffix,
                    COUNT(DISTINCT ts_code) as fund_count,
                    COUNT(*) as record_count
                FROM tushare_fund_portfolio
                GROUP BY 
                    CASE 
                        WHEN ts_code LIKE '%.OF' THEN '.OF'
                        WHEN ts_code LIKE '%.SZ' THEN '.SZ'
                        WHEN ts_code LIKE '%.SH' THEN '.SH'
                        ELSE 'OTHER'
                    END
                ORDER BY fund_count DESC
            """)
            
            result = conn.execute(portfolio_suffix_query)
            portfolio_suffixes = result.fetchall()
            
            logger.info("\ntushare_fund_portfolio表后缀分布:")
            for suffix_info in portfolio_suffixes:
                logger.info(f"  {suffix_info[0]}: {suffix_info[1]} 个基金, {suffix_info[2]} 条记录")
            
            # 2. 查找潜在的不匹配基金
            logger.info("\n2. 潜在不匹配基金分析")
            logger.info("-" * 50)
            
            # 查找基础代码相同但后缀不同的基金
            mismatch_query = text("""
                WITH quarterly_base AS (
                    SELECT 
                        SUBSTRING(fund_code FROM '^(.+)\\.[A-Z]+$') as base_code,
                        fund_code,
                        fund_name
                    FROM fund_quarterly_data
                    WHERE fund_code ~ '^.+\\.[A-Z]+$'
                ),
                portfolio_base AS (
                    SELECT 
                        SUBSTRING(ts_code FROM '^(.+)\\.[A-Z]+$') as base_code,
                        ts_code
                    FROM tushare_fund_portfolio
                    WHERE ts_code ~ '^.+\\.[A-Z]+$'
                    GROUP BY base_code, ts_code
                )
                SELECT 
                    q.base_code,
                    q.fund_code as quarterly_code,
                    q.fund_name,
                    p.ts_code as portfolio_code,
                    CASE 
                        WHEN q.fund_code = p.ts_code THEN 'MATCH'
                        ELSE 'MISMATCH'
                    END as status
                FROM quarterly_base q
                FULL OUTER JOIN portfolio_base p ON q.base_code = p.base_code
                WHERE q.base_code IS NOT NULL AND p.base_code IS NOT NULL
                ORDER BY q.base_code
            """)
            
            result = conn.execute(mismatch_query)
            mismatches = result.fetchall()
            
            match_count = 0
            mismatch_count = 0
            mismatch_examples = []
            
            for match_info in mismatches:
                if match_info[4] == 'MATCH':
                    match_count += 1
                else:
                    mismatch_count += 1
                    if len(mismatch_examples) < 10:  # 只记录前10个例子
                        mismatch_examples.append(match_info)
            
            logger.info(f"基金代码匹配情况:")
            logger.info(f"  完全匹配: {match_count} 个基金")
            logger.info(f"  后缀不匹配: {mismatch_count} 个基金")
            
            if mismatch_examples:
                logger.info(f"\n不匹配示例 (前{len(mismatch_examples)}个):")
                for example in mismatch_examples:
                    logger.info(f"  {example[0]}: {example[1]} vs {example[3]} ({example[2]})")
            
            # 3. 分析LOF基金的特殊情况
            logger.info("\n3. LOF基金特殊情况分析")
            logger.info("-" * 50)
            
            # 查找可能的LOF基金（通常以16开头）
            lof_analysis_query = text("""
                SELECT 
                    q.fund_code,
                    q.fund_name,
                    q.fund_type,
                    CASE WHEN p.ts_code IS NOT NULL THEN 'YES' ELSE 'NO' END as has_portfolio
                FROM fund_quarterly_data q
                LEFT JOIN (
                    SELECT DISTINCT ts_code FROM tushare_fund_portfolio
                ) p ON q.fund_code = p.ts_code
                WHERE q.fund_code LIKE '16%' AND q.is_latest = true
                ORDER BY q.fund_code
            """)
            
            result = conn.execute(lof_analysis_query)
            lof_funds = result.fetchall()
            
            lof_with_portfolio = 0
            lof_without_portfolio = 0
            
            for lof in lof_funds:
                if lof[3] == 'YES':
                    lof_with_portfolio += 1
                else:
                    lof_without_portfolio += 1
            
            logger.info(f"LOF基金分析 (16开头的基金):")
            logger.info(f"  总数: {len(lof_funds)} 个")
            logger.info(f"  有持仓数据: {lof_with_portfolio} 个")
            logger.info(f"  无持仓数据: {lof_without_portfolio} 个")
            
            # 4. 检查具体的后缀转换可能性
            logger.info("\n4. 后缀转换可能性分析")
            logger.info("-" * 50)
            
            conversion_query = text("""
                WITH quarterly_codes AS (
                    SELECT fund_code, fund_name FROM fund_quarterly_data WHERE is_latest = true
                ),
                portfolio_codes AS (
                    SELECT DISTINCT ts_code FROM tushare_fund_portfolio
                )
                SELECT 
                    q.fund_code,
                    q.fund_name,
                    CASE 
                        WHEN q.fund_code LIKE '%.OF' THEN REPLACE(q.fund_code, '.OF', '.SZ')
                        WHEN q.fund_code LIKE '%.SZ' THEN REPLACE(q.fund_code, '.SZ', '.OF')
                        ELSE NULL
                    END as converted_code,
                    CASE 
                        WHEN p.ts_code IS NOT NULL THEN 'DIRECT_MATCH'
                        WHEN p2.ts_code IS NOT NULL THEN 'CONVERSION_MATCH'
                        ELSE 'NO_MATCH'
                    END as match_type
                FROM quarterly_codes q
                LEFT JOIN portfolio_codes p ON q.fund_code = p.ts_code
                LEFT JOIN portfolio_codes p2 ON (
                    CASE 
                        WHEN q.fund_code LIKE '%.OF' THEN REPLACE(q.fund_code, '.OF', '.SZ')
                        WHEN q.fund_code LIKE '%.SZ' THEN REPLACE(q.fund_code, '.SZ', '.OF')
                        ELSE NULL
                    END
                ) = p2.ts_code
                WHERE q.fund_code LIKE '%.OF' OR q.fund_code LIKE '%.SZ'
                ORDER BY match_type, q.fund_code
            """)
            
            result = conn.execute(conversion_query)
            conversions = result.fetchall()
            
            direct_matches = 0
            conversion_matches = 0
            no_matches = 0
            
            conversion_examples = []
            
            for conv in conversions:
                if conv[3] == 'DIRECT_MATCH':
                    direct_matches += 1
                elif conv[3] == 'CONVERSION_MATCH':
                    conversion_matches += 1
                    if len(conversion_examples) < 5:
                        conversion_examples.append(conv)
                else:
                    no_matches += 1
            
            logger.info(f"后缀转换分析:")
            logger.info(f"  直接匹配: {direct_matches} 个基金")
            logger.info(f"  转换后匹配: {conversion_matches} 个基金")
            logger.info(f"  无法匹配: {no_matches} 个基金")
            
            if conversion_examples:
                logger.info(f"\n转换匹配示例:")
                for example in conversion_examples:
                    logger.info(f"  {example[0]} -> {example[2]} ({example[1]})")
                    
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    analyze_fund_code_mismatch()
