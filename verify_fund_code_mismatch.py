#!/usr/bin/env python3
"""
验证163302基金代码后缀不匹配问题
"""

import logging
import os
from dotenv import load_dotenv
from sqlalchemy import create_engine, text

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def verify_fund_code_mismatch():
    """验证基金代码后缀不匹配问题"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    # 构建数据库连接字符串
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            logger.info("=== 163302基金代码后缀不匹配问题验证 ===\n")
            
            # 1. 检查163302.OF的基本信息
            logger.info("1. 检查163302.OF的基本信息...")
            fund_info_query = text("""
                SELECT fund_code, fund_name, fund_manager, report_year, report_quarter, is_latest
                FROM fund_quarterly_data
                WHERE fund_code = '163302.OF'
                ORDER BY report_year DESC, report_quarter DESC
            """)
            
            result = conn.execute(fund_info_query)
            fund_info = result.fetchall()
            
            if fund_info:
                logger.info("163302.OF基本信息:")
                for info in fund_info:
                    logger.info(f"  - {info[0]} | {info[1]} | {info[2]} | {info[3]}Q{info[4]} | 最新: {info[5]}")
            else:
                logger.info("未找到163302.OF的基本信息")
            
            # 2. 检查163302.SZ的持仓数据
            logger.info("\n2. 检查163302.SZ的持仓数据...")
            portfolio_query = text("""
                SELECT ts_code, COUNT(*) as total_records,
                       MIN(end_date) as earliest_date,
                       MAX(end_date) as latest_date
                FROM tushare_fund_portfolio
                WHERE ts_code = '163302.SZ'
                GROUP BY ts_code
            """)
            
            result = conn.execute(portfolio_query)
            portfolio_info = result.fetchall()
            
            if portfolio_info:
                info = portfolio_info[0]
                logger.info(f"163302.SZ持仓数据:")
                logger.info(f"  - 基金代码: {info[0]}")
                logger.info(f"  - 总记录数: {info[1]}")
                logger.info(f"  - 最早日期: {info[2]}")
                logger.info(f"  - 最新日期: {info[3]}")
                
                # 查看最新的持仓数据
                latest_portfolio_query = text("""
                    SELECT symbol, mkv, stk_mkv_ratio
                    FROM tushare_fund_portfolio
                    WHERE ts_code = '163302.SZ'
                    AND end_date = (
                        SELECT MAX(end_date)
                        FROM tushare_fund_portfolio
                        WHERE ts_code = '163302.SZ'
                    )
                    ORDER BY stk_mkv_ratio DESC
                    LIMIT 5
                """)
                
                result = conn.execute(latest_portfolio_query)
                latest_holdings = result.fetchall()
                
                if latest_holdings:
                    logger.info("  最新持仓前5名:")
                    for holding in latest_holdings:
                        logger.info(f"    - {holding[0]} | 市值: {holding[1]} | 占比: {holding[2]}%")
            else:
                logger.info("未找到163302.SZ的持仓数据")
            
            # 3. 检查是否存在163302.OF的持仓数据
            logger.info("\n3. 检查是否存在163302.OF的持仓数据...")
            of_portfolio_query = text("""
                SELECT COUNT(*) as count
                FROM tushare_fund_portfolio
                WHERE ts_code = '163302.OF'
            """)
            
            result = conn.execute(of_portfolio_query)
            of_count = result.fetchone()[0]
            logger.info(f"163302.OF的持仓记录数: {of_count}")
            
            # 4. 检查是否存在163302.SZ的基本信息
            logger.info("\n4. 检查是否存在163302.SZ的基本信息...")
            sz_fund_query = text("""
                SELECT COUNT(*) as count
                FROM fund_quarterly_data
                WHERE fund_code = '163302.SZ'
            """)
            
            result = conn.execute(sz_fund_query)
            sz_count = result.fetchone()[0]
            logger.info(f"163302.SZ的基本信息记录数: {sz_count}")
            
            # 5. 分析问题
            logger.info("\n=== 问题分析 ===")
            logger.info("发现的问题:")
            logger.info("1. 基金基本信息存储为 163302.OF")
            logger.info("2. 基金持仓数据存储为 163302.SZ")
            logger.info("3. 这导致无法正确关联基本信息和持仓数据")
            
            # 6. 建议解决方案
            logger.info("\n=== 建议解决方案 ===")
            logger.info("方案1: 修改数据获取逻辑，支持基金代码后缀转换")
            logger.info("  - 当查询持仓数据时，如果.OF后缀查不到，尝试.SZ后缀")
            logger.info("  - 当查询基本信息时，如果.SZ后缀查不到，尝试.OF后缀")
            
            logger.info("\n方案2: 数据标准化")
            logger.info("  - 统一基金代码后缀格式")
            logger.info("  - 建议使用.OF作为标准后缀")
            
            # 7. 测试代码后缀转换
            logger.info("\n=== 测试代码后缀转换 ===")
            test_codes = ["163302.OF", "163302.SZ"]
            
            for code in test_codes:
                logger.info(f"\n测试基金代码: {code}")
                
                # 尝试获取基本信息
                basic_query = text("""
                    SELECT fund_code, fund_name
                    FROM fund_quarterly_data
                    WHERE fund_code = :code AND is_latest = true
                    LIMIT 1
                """)
                
                result = conn.execute(basic_query, {"code": code})
                basic_info = result.fetchone()
                
                if basic_info:
                    logger.info(f"  ✓ 找到基本信息: {basic_info[0]} - {basic_info[1]}")
                else:
                    logger.info(f"  ✗ 未找到基本信息")
                    
                    # 尝试转换后缀
                    if code.endswith('.OF'):
                        alt_code = code.replace('.OF', '.SZ')
                    elif code.endswith('.SZ'):
                        alt_code = code.replace('.SZ', '.OF')
                    else:
                        alt_code = None
                    
                    if alt_code:
                        result = conn.execute(basic_query, {"code": alt_code})
                        alt_basic_info = result.fetchone()
                        if alt_basic_info:
                            logger.info(f"  ✓ 使用转换后缀 {alt_code} 找到基本信息: {alt_basic_info[1]}")
                
                # 尝试获取持仓数据
                portfolio_query = text("""
                    SELECT COUNT(*) as count
                    FROM tushare_fund_portfolio
                    WHERE ts_code = :code
                """)
                
                result = conn.execute(portfolio_query, {"code": code})
                portfolio_count = result.fetchone()[0]
                
                if portfolio_count > 0:
                    logger.info(f"  ✓ 找到持仓数据: {portfolio_count} 条记录")
                else:
                    logger.info(f"  ✗ 未找到持仓数据")
                    
                    # 尝试转换后缀
                    if code.endswith('.OF'):
                        alt_code = code.replace('.OF', '.SZ')
                    elif code.endswith('.SZ'):
                        alt_code = code.replace('.SZ', '.OF')
                    else:
                        alt_code = None
                    
                    if alt_code:
                        result = conn.execute(portfolio_query, {"code": alt_code})
                        alt_portfolio_count = result.fetchone()[0]
                        if alt_portfolio_count > 0:
                            logger.info(f"  ✓ 使用转换后缀 {alt_code} 找到持仓数据: {alt_portfolio_count} 条记录")
                            
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    verify_fund_code_mismatch()
