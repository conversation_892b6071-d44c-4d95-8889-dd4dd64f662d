<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Weather Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5rem;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .weather-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .weather-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            position: relative;
            overflow: hidden;
            min-height: 400px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .weather-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .weather-card h2 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            z-index: 10;
            position: relative;
        }

        .weather-card .temperature {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            z-index: 10;
            position: relative;
        }

        .weather-card .description {
            font-size: 1.1rem;
            opacity: 0.8;
            z-index: 10;
            position: relative;
        }

        /* Wind Animation */
        .wind-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            opacity: 0.8;
        }

        .cloud1 {
            width: 80px;
            height: 40px;
            top: 20%;
            animation: floatCloud 8s infinite linear;
        }

        .cloud2 {
            width: 60px;
            height: 30px;
            top: 40%;
            animation: floatCloud 12s infinite linear;
            animation-delay: -4s;
        }

        .wind-lines {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .wind-line {
            position: absolute;
            height: 2px;
            background: rgba(255, 255, 255, 0.4);
            animation: windBlow 3s infinite ease-in-out;
        }

        @keyframes floatCloud {
            0% { transform: translateX(-100px); }
            100% { transform: translateX(400px); }
        }

        @keyframes windBlow {
            0%, 100% { transform: translateX(-20px); opacity: 0; }
            50% { transform: translateX(20px); opacity: 1; }
        }

        /* Rain Animation */
        .rain-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .raindrop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, rgba(173, 216, 230, 0.8));
            animation: fall linear infinite;
        }

        @keyframes fall {
            0% { transform: translateY(-100px); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(500px); opacity: 0; }
        }

        /* Sun Animation */
        .sun-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .sun {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, #FFD700, #FFA500);
            border-radius: 50%;
            animation: sunPulse 2s infinite ease-in-out;
        }

        .sun-ray {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100px;
            height: 3px;
            background: linear-gradient(to right, transparent, #FFD700, transparent);
            transform-origin: left center;
            animation: rotateRays 4s infinite linear;
        }

        @keyframes sunPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
        }

        @keyframes rotateRays {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Snow Animation */
        .snow-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .snowflake {
            position: absolute;
            color: white;
            font-size: 1em;
            animation: snowfall linear infinite;
        }

        @keyframes snowfall {
            0% { transform: translateY(-100px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(500px) rotate(360deg); opacity: 0; }
        }

        /* Controls */
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .control-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: transparent;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .weather-grid {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .control-btn {
                display: block;
                margin: 10px auto;
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Animated Weather Cards</h1>
        
        <div class="controls">
            <button class="control-btn active" onclick="showAllCards()">Show All</button>
            <button class="control-btn" onclick="showCard('wind')">Wind</button>
            <button class="control-btn" onclick="showCard('rain')">Rain</button>
            <button class="control-btn" onclick="showCard('sun')">Sun</button>
            <button class="control-btn" onclick="showCard('snow')">Snow</button>
        </div>

        <div class="weather-grid">
            <!-- Wind Card -->
            <div class="weather-card" id="wind-card">
                <h2>Windy</h2>
                <div class="temperature">18°C</div>
                <div class="description">Strong winds with moving clouds</div>
                
                <div class="wind-container">
                    <div class="cloud cloud1"></div>
                    <div class="cloud cloud2"></div>
                    <div class="wind-lines">
                        <div class="wind-line" style="top: 30%; left: 10%; width: 30px; animation-delay: 0s;"></div>
                        <div class="wind-line" style="top: 50%; left: 20%; width: 40px; animation-delay: 0.5s;"></div>
                        <div class="wind-line" style="top: 70%; left: 15%; width: 35px; animation-delay: 1s;"></div>
                        <div class="wind-line" style="top: 25%; left: 60%; width: 25px; animation-delay: 1.5s;"></div>
                        <div class="wind-line" style="top: 65%; left: 70%; width: 45px; animation-delay: 2s;"></div>
                    </div>
                </div>
            </div>

            <!-- Rain Card -->
            <div class="weather-card" id="rain-card">
                <h2>Rainy</h2>
                <div class="temperature">15°C</div>
                <div class="description">Heavy rainfall with puddles</div>
                
                <div class="rain-container" id="rain-container"></div>
            </div>

            <!-- Sun Card -->
            <div class="weather-card" id="sun-card">
                <h2>Sunny</h2>
                <div class="temperature">28°C</div>
                <div class="description">Bright sunshine and clear skies</div>
                
                <div class="sun-container">
                    <div class="sun"></div>
                    <div class="sun-ray" style="transform: rotate(0deg);"></div>
                    <div class="sun-ray" style="transform: rotate(45deg);"></div>
                    <div class="sun-ray" style="transform: rotate(90deg);"></div>
                    <div class="sun-ray" style="transform: rotate(135deg);"></div>
                    <div class="sun-ray" style="transform: rotate(180deg);"></div>
                    <div class="sun-ray" style="transform: rotate(225deg);"></div>
                    <div class="sun-ray" style="transform: rotate(270deg);"></div>
                    <div class="sun-ray" style="transform: rotate(315deg);"></div>
                </div>
            </div>

            <!-- Snow Card -->
            <div class="weather-card" id="snow-card">
                <h2>Snowy</h2>
                <div class="temperature">-2°C</div>
                <div class="description">Snowfall with accumulation</div>
                
                <div class="snow-container" id="snow-container"></div>
            </div>
        </div>
    </div>

    <script>
        // Weather animation management
        let currentView = 'all';
        
        // Initialize animations
        function initializeAnimations() {
            createRain();
            createSnow();
        }

        // Create raindrops
        function createRain() {
            const rainContainer = document.getElementById('rain-container');
            const raindrops = 50;
            
            for (let i = 0; i < raindrops; i++) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + '%';
                raindrop.style.animationDuration = (Math.random() * 1 + 0.5) + 's';
                raindrop.style.animationDelay = Math.random() * 2 + 's';
                rainContainer.appendChild(raindrop);
            }
        }

        // Create snowflakes
        function createSnow() {
            const snowContainer = document.getElementById('snow-container');
            const snowflakes = 30;
            const snowflakeSymbols = ['❄', '❅', '❆', '•', '*'];
            
            for (let i = 0; i < snowflakes; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.innerHTML = snowflakeSymbols[Math.floor(Math.random() * snowflakeSymbols.length)];
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDuration = (Math.random() * 3 + 2) + 's';
                snowflake.style.animationDelay = Math.random() * 2 + 's';
                snowflake.style.fontSize = (Math.random() * 0.5 + 0.5) + 'em';
                snowContainer.appendChild(snowflake);
            }
        }

        // Show specific card
        function showCard(weatherType) {
            const cards = document.querySelectorAll('.weather-card');
            const buttons = document.querySelectorAll('.control-btn');
            
            // Update active button
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide cards
            cards.forEach(card => {
                if (weatherType === 'all' || card.id === weatherType + '-card') {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
            
            currentView = weatherType;
        }

        // Show all cards
        function showAllCards() {
            const cards = document.querySelectorAll('.weather-card');
            const buttons = document.querySelectorAll('.control-btn');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            buttons[0].classList.add('active');
            
            cards.forEach(card => {
                card.style.display = 'block';
            });
            
            currentView = 'all';
        }

        // Add dynamic wind lines
        function addWindLines() {
            const windContainer = document.querySelector('#wind-card .wind-lines');
            if (windContainer.children.length < 8) {
                const windLine = document.createElement('div');
                windLine.className = 'wind-line';
                windLine.style.top = Math.random() * 80 + 10 + '%';
                windLine.style.left = Math.random() * 50 + '%';
                windLine.style.width = (Math.random() * 30 + 20) + 'px';
                windLine.style.animationDelay = Math.random() * 3 + 's';
                windContainer.appendChild(windLine);
                
                setTimeout(() => {
                    if (windLine.parentNode) {
                        windLine.parentNode.removeChild(windLine);
                    }
                }, 3000);
            }
        }

        // Add dynamic raindrops
        function addRaindrops() {
            const rainContainer = document.getElementById('rain-container');
            if (currentView === 'rain' || currentView === 'all') {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + '%';
                raindrop.style.animationDuration = (Math.random() * 1 + 0.5) + 's';
                rainContainer.appendChild(raindrop);
                
                setTimeout(() => {
                    if (raindrop.parentNode) {
                        raindrop.parentNode.removeChild(raindrop);
                    }
                }, 2000);
            }
        }

        // Add dynamic snowflakes
        function addSnowflakes() {
            const snowContainer = document.getElementById('snow-container');
            if (currentView === 'snow' || currentView === 'all') {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.innerHTML = ['❄', '❅', '❆', '•', '*'][Math.floor(Math.random() * 5)];
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDuration = (Math.random() * 3 + 2) + 's';
                snowflake.style.fontSize = (Math.random() * 0.5 + 0.5) + 'em';
                snowContainer.appendChild(snowflake);
                
                setTimeout(() => {
                    if (snowflake.parentNode) {
                        snowflake.parentNode.removeChild(snowflake);
                    }
                }, 5000);
            }
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();
            
            // Add dynamic elements periodically
            setInterval(addWindLines, 1000);
            setInterval(addRaindrops, 200);
            setInterval(addSnowflakes, 500);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    showCard('wind');
                    break;
                case '2':
                    showCard('rain');
                    break;
                case '3':
                    showCard('sun');
                    break;
                case '4':
                    showCard('snow');
                    break;
                case '0':
                    showAllCards();
                    break;
            }
        });
    </script>
</body>
</html>
