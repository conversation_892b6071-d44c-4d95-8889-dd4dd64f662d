#!/usr/bin/env python3
"""
测试基金代码后缀转换功能
"""

import logging
import os
import time
from dotenv import load_dotenv
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data, convert_fund_code_suffix

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def test_suffix_conversion_function():
    """测试后缀转换函数"""
    logger = logging.getLogger(__name__)
    
    logger.info("=== 测试后缀转换函数 ===")
    
    test_cases = [
        "163302.OF",
        "163302.SZ", 
        "159150.OF",
        "159150.SZ",
        "510050.SH",
        "510050.OF",
        "000001",  # 无后缀
        "invalid",  # 无效格式
    ]
    
    for test_code in test_cases:
        converted = convert_fund_code_suffix(test_code)
        logger.info(f"{test_code} -> {converted}")

def test_portfolio_data_with_conversion():
    """测试持仓数据获取的后缀转换功能"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    logger.info("\n=== 测试持仓数据获取的后缀转换功能 ===")
    
    # 测试用例：已知存在后缀不匹配的基金
    test_funds = [
        "163302.OF",  # 应该能通过转换找到163302.SZ的数据
        "159150.OF",  # ETF基金，可能需要转换为.SZ
        "159201.OF",  # 另一个ETF基金
        "000001.OF",  # 普通基金，测试不影响正常查询
    ]
    
    for fund_code in test_funds:
        logger.info(f"\n{'='*60}")
        logger.info(f"测试基金: {fund_code}")
        logger.info(f"{'='*60}")
        
        # 1. 获取基金基本信息
        start_time = time.time()
        fund_data = fetch_fund_data(fund_code)
        basic_time = time.time() - start_time
        
        if fund_data is None:
            logger.warning(f"基金 {fund_code} 无基本信息，跳过持仓测试")
            continue
            
        logger.info(f"基金名称: {fund_data.get('fund_name', '未知')}")
        logger.info(f"基金经理: {fund_data.get('fund_manager', '未知')}")
        logger.info(f"报告期: {fund_data.get('report_end_date', '未知')}")
        logger.info(f"基本信息查询耗时: {basic_time:.3f}秒")
        
        # 2. 获取持仓数据（包含后缀转换逻辑）
        report_end_date = fund_data.get('report_end_date')
        if not report_end_date or report_end_date == "未知报告日期":
            logger.warning(f"基金 {fund_code} 报告期无效，跳过持仓测试")
            continue
            
        start_time = time.time()
        portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)
        portfolio_time = time.time() - start_time
        
        logger.info(f"持仓数据查询耗时: {portfolio_time:.3f}秒")
        
        if portfolio_data is None:
            logger.warning(f"基金 {fund_code} 持仓数据获取失败（包括后缀转换尝试）")
        elif not portfolio_data:
            logger.info(f"基金 {fund_code} 持仓数据为空")
        else:
            logger.info(f"✅ 基金 {fund_code} 成功获取 {len(portfolio_data)} 条持仓数据")
            
            # 显示前3条持仓
            logger.info("前3大持仓:")
            for i, holding in enumerate(portfolio_data[:3]):
                logger.info(f"  {i+1}. {holding.get('symbol', 'N/A')} - "
                          f"{holding.get('stock_name', 'N/A')} - "
                          f"占比: {holding.get('stk_mkv_ratio', 'N/A')}%")

def test_performance_impact():
    """测试性能影响"""
    logger = logging.getLogger(__name__)
    
    load_dotenv()
    
    logger.info("\n=== 性能影响测试 ===")
    
    # 测试正常基金（不需要转换）的性能
    normal_fund = "000001.OF"
    fund_data = fetch_fund_data(normal_fund)
    
    if fund_data and fund_data.get('report_end_date'):
        report_date = fund_data.get('report_end_date')
        
        # 多次查询测试性能
        times = []
        for i in range(5):
            start_time = time.time()
            portfolio_data = fetch_fund_portfolio_data(normal_fund, report_date)
            end_time = time.time()
            times.append(end_time - start_time)
            
        avg_time = sum(times) / len(times)
        logger.info(f"正常基金查询平均耗时: {avg_time:.3f}秒 (5次测试)")
        logger.info(f"查询时间范围: {min(times):.3f}s - {max(times):.3f}s")
    
    # 测试需要转换的基金性能
    conversion_fund = "163302.OF"
    fund_data = fetch_fund_data(conversion_fund)
    
    if fund_data and fund_data.get('report_end_date'):
        report_date = fund_data.get('report_end_date')
        
        times = []
        for i in range(3):  # 转换查询次数少一些，避免过多日志
            start_time = time.time()
            portfolio_data = fetch_fund_portfolio_data(conversion_fund, report_date)
            end_time = time.time()
            times.append(end_time - start_time)
            
        avg_time = sum(times) / len(times)
        logger.info(f"转换基金查询平均耗时: {avg_time:.3f}秒 (3次测试)")
        logger.info(f"查询时间范围: {min(times):.3f}s - {max(times):.3f}s")

def main():
    """主测试函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始基金代码后缀转换功能测试")
    
    try:
        # 1. 测试转换函数
        test_suffix_conversion_function()
        
        # 2. 测试实际数据获取
        test_portfolio_data_with_conversion()
        
        # 3. 测试性能影响
        test_performance_impact()
        
        logger.info("\n=== 测试完成 ===")
        logger.info("如果看到 ✅ 标记，说明后缀转换功能正常工作")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
