/**
 * 数据格式化工具函数
 */

import dayjs from 'dayjs'

/**
 * 格式化数字
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @param {boolean} thousands 是否显示千分位
 * @returns {string} 格式化后的数字
 */
export const formatNumber = (num, decimals = 2, thousands = true) => {
  if (num === null || num === undefined || isNaN(num)) {
    return '--'
  }
  
  const number = Number(num)
  const formatted = number.toFixed(decimals)
  
  if (thousands) {
    return formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }
  
  return formatted
}

/**
 * 格式化百分比
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的百分比
 */
export const formatPercent = (num, decimals = 2) => {
  if (num === null || num === undefined || isNaN(num)) {
    return '--'
  }
  
  const percent = Number(num) * 100
  return `${percent.toFixed(decimals)}%`
}

/**
 * 格式化金额
 * @param {number} amount 金额
 * @param {string} unit 单位 (元, 万元, 亿元)
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的金额
 */
export const formatAmount = (amount, unit = '元', decimals = 2) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '--'
  }
  
  let value = Number(amount)
  let unitText = unit
  
  // 自动转换单位
  if (unit === 'auto') {
    if (value >= 100000000) {
      value = value / 100000000
      unitText = '亿元'
    } else if (value >= 10000) {
      value = value / 10000
      unitText = '万元'
    } else {
      unitText = '元'
    }
  }
  
  return `${formatNumber(value, decimals)}${unitText}`
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) {
    return '--'
  }
  
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param {string|Date} date 日期
 * @returns {string} 相对时间
 */
export const formatRelativeTime = (date) => {
  if (!date) {
    return '--'
  }
  
  const now = dayjs()
  const target = dayjs(date)
  const diff = now.diff(target, 'day')
  
  if (diff === 0) {
    return '今天'
  } else if (diff === 1) {
    return '昨天'
  } else if (diff < 7) {
    return `${diff}天前`
  } else if (diff < 30) {
    const weeks = Math.floor(diff / 7)
    return `${weeks}周前`
  } else if (diff < 365) {
    const months = Math.floor(diff / 30)
    return `${months}个月前`
  } else {
    const years = Math.floor(diff / 365)
    return `${years}年前`
  }
}

/**
 * 格式化基金代码
 * @param {string} code 基金代码
 * @returns {string} 格式化后的基金代码
 */
export const formatFundCode = (code) => {
  if (!code) {
    return '--'
  }
  
  // 移除.OF后缀
  return code.replace(/\.OF$/, '')
}

/**
 * 格式化基金规模
 * @param {number} scale 规模（亿元）
 * @returns {string} 格式化后的规模
 */
export const formatFundScale = (scale) => {
  if (scale === null || scale === undefined || isNaN(scale)) {
    return '--'
  }
  
  const value = Number(scale)
  
  if (value >= 100) {
    return `${formatNumber(value, 0)}亿元`
  } else if (value >= 10) {
    return `${formatNumber(value, 1)}亿元`
  } else {
    return `${formatNumber(value, 2)}亿元`
  }
}

/**
 * 格式化收益率
 * @param {number} rate 收益率
 * @param {boolean} showSign 是否显示正负号
 * @returns {string} 格式化后的收益率
 */
export const formatReturn = (rate, showSign = true) => {
  if (rate === null || rate === undefined || isNaN(rate)) {
    return '--'
  }
  
  const value = Number(rate)
  const percent = formatPercent(value)
  
  if (showSign && value > 0) {
    return `+${percent}`
  }
  
  return percent
}

/**
 * 格式化风险等级
 * @param {number} level 风险等级 (1-5)
 * @returns {string} 风险等级文本
 */
export const formatRiskLevel = (level) => {
  const riskMap = {
    1: '低风险',
    2: '中低风险',
    3: '中风险',
    4: '中高风险',
    5: '高风险'
  }
  
  return riskMap[level] || '--'
}

/**
 * 格式化基金类型
 * @param {string} type 基金类型代码
 * @returns {string} 基金类型名称
 */
export const formatFundType = (type) => {
  const typeMap = {
    'stock': '股票型',
    'bond': '债券型',
    'hybrid': '混合型',
    'money': '货币型',
    'index': '指数型',
    'qdii': 'QDII',
    'fof': 'FOF'
  }
  
  return typeMap[type] || type || '--'
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 格式化持仓权重
 * @param {number} weight 权重
 * @returns {string} 格式化后的权重
 */
export const formatWeight = (weight) => {
  if (weight === null || weight === undefined || isNaN(weight)) {
    return '--'
  }
  
  return formatPercent(weight / 100) // 假设传入的是百分比数值
}

/**
 * 截断文本
 * @param {string} text 文本
 * @param {number} length 最大长度
 * @param {string} suffix 后缀
 * @returns {string} 截断后的文本
 */
export const truncateText = (text, length = 50, suffix = '...') => {
  if (!text || text.length <= length) {
    return text || '--'
  }
  
  return text.substring(0, length) + suffix
}

/**
 * 格式化股票代码
 * @param {string} code 股票代码
 * @returns {string} 格式化后的股票代码
 */
export const formatStockCode = (code) => {
  if (!code) {
    return '--'
  }
  
  // 添加市场后缀
  if (/^\d{6}$/.test(code)) {
    if (code.startsWith('6')) {
      return `${code}.SH`
    } else if (code.startsWith('0') || code.startsWith('3')) {
      return `${code}.SZ`
    }
  }
  
  return code
}
