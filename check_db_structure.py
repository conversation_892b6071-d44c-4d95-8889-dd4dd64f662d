#!/usr/bin/env python3
"""
检查数据库表结构和基金代码存在情况
"""

import logging
import os
from dotenv import load_dotenv
from sqlalchemy import create_engine, text

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def check_database_structure():
    """检查数据库表结构和基金代码存在情况"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    # 构建数据库连接字符串
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # 1. 检查fund_quarterly_data表结构
            logger.info("1. 检查fund_quarterly_data表结构...")
            table_info_query = text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'fund_quarterly_data'
                ORDER BY ordinal_position
            """)
            
            result = conn.execute(table_info_query)
            columns = result.fetchall()
            
            if columns:
                logger.info("fund_quarterly_data表字段:")
                for col in columns:
                    logger.info(f"  - {col[0]} ({col[1]}) - 可空: {col[2]}")
            else:
                logger.warning("未找到fund_quarterly_data表")
            
            # 2. 检查tushare_fund_portfolio表结构
            logger.info("\n2. 检查tushare_fund_portfolio表结构...")
            portfolio_info_query = text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'tushare_fund_portfolio'
                ORDER BY ordinal_position
            """)
            
            result = conn.execute(portfolio_info_query)
            columns = result.fetchall()
            
            if columns:
                logger.info("tushare_fund_portfolio表字段:")
                for col in columns:
                    logger.info(f"  - {col[0]} ({col[1]}) - 可空: {col[2]}")
            else:
                logger.warning("未找到tushare_fund_portfolio表")
            
            # 3. 查找包含163302的基金代码
            logger.info("\n3. 查找包含163302的基金代码...")
            
            # 在fund_quarterly_data表中查找
            fund_search_query = text("""
                SELECT DISTINCT fund_code, fund_name
                FROM fund_quarterly_data
                WHERE fund_code LIKE '%163302%'
                ORDER BY fund_code
            """)
            
            result = conn.execute(fund_search_query)
            funds = result.fetchall()
            
            if funds:
                logger.info("在fund_quarterly_data表中找到的163302相关基金:")
                for fund in funds:
                    logger.info(f"  - {fund[0]}: {fund[1]}")
            else:
                logger.info("在fund_quarterly_data表中未找到163302相关基金")
            
            # 在tushare_fund_portfolio表中查找
            portfolio_search_query = text("""
                SELECT DISTINCT ts_code, COUNT(*) as record_count
                FROM tushare_fund_portfolio
                WHERE ts_code LIKE '%163302%'
                GROUP BY ts_code
                ORDER BY ts_code
            """)
            
            result = conn.execute(portfolio_search_query)
            portfolios = result.fetchall()
            
            if portfolios:
                logger.info("在tushare_fund_portfolio表中找到的163302相关基金:")
                for portfolio in portfolios:
                    logger.info(f"  - {portfolio[0]}: {portfolio[1]} 条持仓记录")
            else:
                logger.info("在tushare_fund_portfolio表中未找到163302相关基金")
            
            # 4. 检查基金代码的格式模式
            logger.info("\n4. 检查基金代码格式模式...")
            
            # 查看fund_quarterly_data中的基金代码格式
            code_pattern_query = text("""
                SELECT fund_code, COUNT(*) as count
                FROM fund_quarterly_data
                WHERE fund_code LIKE '%163302%' OR fund_code LIKE '163302%'
                GROUP BY fund_code
                ORDER BY fund_code
            """)
            
            result = conn.execute(code_pattern_query)
            patterns = result.fetchall()
            
            if patterns:
                logger.info("fund_quarterly_data中163302相关的代码格式:")
                for pattern in patterns:
                    logger.info(f"  - {pattern[0]}: {pattern[1]} 条记录")
            
            # 查看tushare_fund_portfolio中的基金代码格式
            portfolio_pattern_query = text("""
                SELECT ts_code, COUNT(*) as count
                FROM tushare_fund_portfolio
                WHERE ts_code LIKE '%163302%' OR ts_code LIKE '163302%'
                GROUP BY ts_code
                ORDER BY ts_code
            """)
            
            result = conn.execute(portfolio_pattern_query)
            portfolio_patterns = result.fetchall()
            
            if portfolio_patterns:
                logger.info("tushare_fund_portfolio中163302相关的代码格式:")
                for pattern in portfolio_patterns:
                    logger.info(f"  - {pattern[0]}: {pattern[1]} 条记录")
                    
    except Exception as e:
        logger.error(f"检查数据库时发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    check_database_structure()
