# 基金分析系统前端技术规范文档

## 1. 技术栈选型

### 1.1 核心技术栈
```json
{
  "框架": "Vue 3.4+ (Composition API)",
  "UI组件库": "Element Plus 2.4+",
  "图表库": "ECharts 5.4+ + Vue-ECharts 6.6+",
  "状态管理": "Pinia 2.1+",
  "路由管理": "Vue Router 4.2+",
  "HTTP客户端": "Axios 1.6+",
  "构建工具": "Vite 5.0+",
  "包管理器": "npm 8.0+"
}
```

### 1.2 开发工具链
```json
{
  "代码规范": "ESLint + Prettier",
  "自动导入": "unplugin-auto-import",
  "组件自动注册": "unplugin-vue-components",
  "CSS预处理器": "原生CSS + CSS Variables",
  "图标库": "@element-plus/icons-vue",
  "工具库": "dayjs, file-saver, xlsx"
}
```

### 1.3 浏览器兼容性
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **移动端**: iOS Safari 14+, Android Chrome 90+
- **不支持**: IE 11及以下版本

## 2. 项目架构设计

### 2.1 目录结构
```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口层
│   │   ├── fund.js        # 基金相关接口
│   │   ├── report.js      # 报告相关接口
│   │   └── index.js       # 接口统一导出
│   ├── components/        # 通用组件
│   │   ├── common/        # 基础组件
│   │   ├── charts/        # 图表组件
│   │   └── business/      # 业务组件
│   ├── views/             # 页面组件
│   │   ├── fund/          # 基金相关页面
│   │   ├── report/        # 报告相关页面
│   │   └── layout/        # 布局组件
│   ├── stores/            # 状态管理
│   │   ├── fund.js        # 基金数据状态
│   │   ├── user.js        # 用户状态
│   │   └── app.js         # 应用全局状态
│   ├── utils/             # 工具函数
│   │   ├── request.js     # HTTP请求封装
│   │   ├── format.js      # 数据格式化
│   │   ├── chart.js       # 图表配置
│   │   └── export.js      # 导出功能
│   ├── styles/            # 样式文件
│   │   ├── index.css      # 全局样式
│   │   ├── variables.css  # CSS变量
│   │   └── components.css # 组件样式
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── App.vue            # 根组件
│   └── main.js            # 应用入口
├── package.json           # 项目配置
├── vite.config.js         # Vite配置
└── index.html             # HTML模板
```

### 2.2 数据流架构
```
用户操作 → 组件事件 → Store Action → API请求 → 后端服务
                                    ↓
组件更新 ← 响应式数据 ← Store State ← API响应 ← 数据处理
```

## 3. API接口设计

### 3.1 接口规范
```javascript
// 统一响应格式
{
  "code": 200,           // 状态码
  "message": "success",  // 响应消息
  "data": {},           // 响应数据
  "timestamp": 1640995200000  // 时间戳
}

// 分页响应格式
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],         // 数据列表
    "total": 100,       // 总数量
    "page": 1,          // 当前页
    "pageSize": 20      // 每页数量
  }
}
```

### 3.2 核心接口定义
```javascript
// 基金相关接口
GET  /api/funds                    // 获取基金列表
GET  /api/funds/:code              // 获取基金详情
GET  /api/funds/:code/portfolio    // 获取基金持仓
GET  /api/funds/:code/performance  // 获取基金业绩
POST /api/funds/compare            // 基金对比分析

// 报告相关接口
GET  /api/reports/:fundCode        // 获取分析报告
POST /api/reports/export           // 导出报告
GET  /api/reports/download/:id     // 下载报告文件

// 数据相关接口
POST /api/data/upload              // 上传数据文件
GET  /api/data/template            // 下载模板文件
```

### 3.3 请求/响应示例
```javascript
// 获取基金列表请求
GET /api/funds?page=1&pageSize=20&type=股票型&keyword=华夏

// 响应
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "code": "000001.OF",
        "name": "华夏成长混合",
        "manager": "张三",
        "netValue": 1.2345,
        "totalReturn": 0.1234,
        "scale": 50.67
      }
    ],
    "total": 150,
    "page": 1,
    "pageSize": 20
  }
}
```

## 4. 组件设计规范

### 4.1 组件分类
```javascript
// 1. 基础组件 (components/common/)
- BaseTable.vue      // 基础表格组件
- BaseChart.vue      // 基础图表组件
- BaseUpload.vue     // 基础上传组件
- BaseExport.vue     // 基础导出组件

// 2. 业务组件 (components/business/)
- FundTable.vue      // 基金数据表格
- FundChart.vue      // 基金图表组件
- FundFilter.vue     // 基金筛选组件
- FundCompare.vue    // 基金对比组件

// 3. 页面组件 (views/)
- FundList.vue       // 基金列表页
- FundDetail.vue     // 基金详情页
- FundCompare.vue    // 基金对比页
- ReportCenter.vue   // 报告中心页
```

### 4.2 组件命名规范
```javascript
// 组件文件命名：PascalCase
FundTable.vue
BaseChart.vue

// 组件注册名：kebab-case
<fund-table />
<base-chart />

// Props命名：camelCase
const props = defineProps({
  fundData: Array,
  chartType: String
})

// 事件命名：kebab-case
emit('fund-selected', fundCode)
emit('chart-updated', chartData)
```

### 4.3 组件结构模板
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'

// 2. 定义Props
const props = defineProps({
  // props定义
})

// 3. 定义Emits
const emit = defineEmits(['event-name'])

// 4. 响应式数据
const state = ref({})

// 5. 计算属性
const computedValue = computed(() => {})

// 6. 方法定义
const handleMethod = () => {}

// 7. 生命周期
onMounted(() => {})
</script>

<style scoped>
.component-name {
  /* 组件样式 */
}
</style>
```

## 5. 状态管理规范

### 5.1 Store结构设计
```javascript
// stores/fund.js
export const useFundStore = defineStore('fund', () => {
  // 状态定义
  const fundList = ref([])
  const currentFund = ref(null)
  const loading = ref(false)
  
  // Getters
  const activeFunds = computed(() => 
    fundList.value.filter(fund => fund.status === 'active')
  )
  
  // Actions
  const fetchFundList = async (params) => {
    loading.value = true
    try {
      const response = await fundApi.getFundList(params)
      fundList.value = response.data.list
    } finally {
      loading.value = false
    }
  }
  
  return {
    // 状态
    fundList,
    currentFund,
    loading,
    // 计算属性
    activeFunds,
    // 方法
    fetchFundList
  }
})
```

### 5.2 状态管理最佳实践
- 使用Composition API风格的Pinia
- 按功能模块划分Store
- 异步操作统一在Actions中处理
- 使用computed进行数据派生
- 避免直接修改state，通过actions修改

## 6. 样式规范

### 6.1 CSS变量定义
```css
/* styles/variables.css */
:root {
  /* 主色调 */
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  
  /* 文字颜色 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  /* 边框颜色 */
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  
  /* 背景颜色 */
  --bg-color: #ffffff;
  --bg-page: #f2f3f5;
  --bg-overlay: rgba(0, 0, 0, 0.8);
}
```

### 6.2 响应式断点
```css
/* 响应式断点 */
@media (max-width: 768px) {
  /* 移动端样式 */
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* 平板端样式 */
}

@media (min-width: 1025px) {
  /* 桌面端样式 */
}
```

## 7. 性能优化规范

### 7.1 代码分割
```javascript
// 路由懒加载
const FundList = () => import('@/views/fund/FundList.vue')
const FundDetail = () => import('@/views/fund/FundDetail.vue')

// 组件懒加载
const AsyncChart = defineAsyncComponent(() => 
  import('@/components/charts/BaseChart.vue')
)
```

### 7.2 缓存策略
```javascript
// API响应缓存
const cache = new Map()
const getCachedData = (key, fetcher, ttl = 5 * 60 * 1000) => {
  const cached = cache.get(key)
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  
  const data = fetcher()
  cache.set(key, { data, timestamp: Date.now() })
  return data
}
```

### 7.3 图表优化
```javascript
// ECharts按需引入
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'

use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent
])
```

## 8. 错误处理规范

### 8.1 全局错误处理
```javascript
// utils/request.js
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 未授权处理
    } else if (error.response?.status === 500) {
      // 服务器错误处理
    }
    return Promise.reject(error)
  }
)
```

### 8.2 组件错误边界
```vue
<template>
  <div v-if="error" class="error-boundary">
    <el-alert type="error" :title="error.message" show-icon />
  </div>
  <div v-else>
    <slot />
  </div>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'

const error = ref(null)

onErrorCaptured((err) => {
  error.value = err
  return false
})
</script>
```

## 9. 测试规范

### 9.1 单元测试
```javascript
// 使用Vitest进行单元测试
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import FundTable from '@/components/business/FundTable.vue'

describe('FundTable', () => {
  it('renders fund data correctly', () => {
    const wrapper = mount(FundTable, {
      props: {
        fundData: mockFundData
      }
    })
    expect(wrapper.find('.fund-table').exists()).toBe(true)
  })
})
```

### 9.2 E2E测试
```javascript
// 使用Playwright进行E2E测试
import { test, expect } from '@playwright/test'

test('fund list page', async ({ page }) => {
  await page.goto('/funds')
  await expect(page.locator('.fund-table')).toBeVisible()
  await expect(page.locator('.fund-item')).toHaveCount(20)
})
```
