#!/usr/bin/env python3
"""
测试脚本：检查163302.OF和163302.SZ的持仓数据获取情况
"""

import logging
import os
from dotenv import load_dotenv
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def test_fund_portfolio_data():
    """测试163302.OF和163302.SZ的持仓数据获取"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()

    logger.info("数据库引擎已在模块加载时自动初始化")
    
    # 测试的基金代码
    test_funds = ["163302.OF", "163302.SZ"]
    
    for fund_code in test_funds:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试基金代码: {fund_code}")
        logger.info(f"{'='*50}")
        
        # 1. 获取基金基本数据
        logger.info(f"1. 获取基金 {fund_code} 的基本数据...")
        fund_data = fetch_fund_data(fund_code)
        
        if fund_data is None:
            logger.error(f"基金 {fund_code} 的基本数据获取失败")
            continue
        
        logger.info(f"基金名称: {fund_data.get('fund_name', '未知')}")
        logger.info(f"基金经理: {fund_data.get('fund_manager', '未知')}")
        logger.info(f"报告期截止日: {fund_data.get('report_end_date', '未知')}")
        
        # 2. 获取持仓数据
        report_end_date = fund_data.get('report_end_date')
        if not report_end_date or report_end_date == "未知报告日期":
            logger.warning(f"基金 {fund_code} 的报告期截止日期无效，跳过持仓数据测试")
            continue
            
        logger.info(f"2. 获取基金 {fund_code} 在 {report_end_date} 的持仓数据...")
        portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)
        
        if portfolio_data is None:
            logger.warning(f"基金 {fund_code} 的持仓数据获取失败或为空")
        elif not portfolio_data:
            logger.info(f"基金 {fund_code} 的持仓数据为空列表")
        else:
            logger.info(f"基金 {fund_code} 成功获取到 {len(portfolio_data)} 条持仓数据")
            
            # 显示前3条持仓数据的详细信息
            for i, holding in enumerate(portfolio_data[:3]):
                logger.info(f"  持仓 {i+1}: {holding.get('symbol', 'N/A')} - "
                          f"{holding.get('stock_name', 'N/A')} - "
                          f"占比: {holding.get('stk_mkv_ratio', 'N/A')}%")
        
        # 3. 检查数据库中是否存在该基金代码的记录
        logger.info(f"3. 检查数据库中基金 {fund_code} 的记录情况...")
        check_fund_in_database(fund_code)

def check_fund_in_database(fund_code):
    """检查基金代码在数据库中的存在情况"""
    logger = logging.getLogger(__name__)
    
    try:
        from src.data_fetcher import engine
        from sqlalchemy import text
        
        if not engine:
            logger.error("数据库引擎未初始化")
            return
            
        with engine.connect() as conn:
            # 检查fund_quarterly_data表
            quarterly_query = text("""
                SELECT fund_code, fund_name, report_end_date, is_latest 
                FROM fund_quarterly_data 
                WHERE fund_code = :fund_code 
                ORDER BY report_end_date DESC 
                LIMIT 5
            """)
            
            quarterly_result = conn.execute(quarterly_query, {"fund_code": fund_code})
            quarterly_rows = quarterly_result.fetchall()
            
            if quarterly_rows:
                logger.info(f"  在fund_quarterly_data表中找到 {len(quarterly_rows)} 条记录:")
                for row in quarterly_rows:
                    logger.info(f"    - {row[0]} | {row[1]} | {row[2]} | 最新: {row[3]}")
            else:
                logger.warning(f"  在fund_quarterly_data表中未找到基金 {fund_code} 的记录")
            
            # 检查tushare_fund_portfolio表
            portfolio_query = text("""
                SELECT ts_code, COUNT(*) as record_count, 
                       MIN(end_date) as earliest_date, 
                       MAX(end_date) as latest_date
                FROM tushare_fund_portfolio 
                WHERE ts_code = :fund_code 
                GROUP BY ts_code
            """)
            
            portfolio_result = conn.execute(portfolio_query, {"fund_code": fund_code})
            portfolio_rows = portfolio_result.fetchall()
            
            if portfolio_rows:
                row = portfolio_rows[0]
                logger.info(f"  在tushare_fund_portfolio表中找到记录:")
                logger.info(f"    - 基金代码: {row[0]}")
                logger.info(f"    - 记录总数: {row[1]}")
                logger.info(f"    - 最早日期: {row[2]}")
                logger.info(f"    - 最新日期: {row[3]}")
            else:
                logger.warning(f"  在tushare_fund_portfolio表中未找到基金 {fund_code} 的记录")
                
    except Exception as e:
        logger.error(f"检查数据库记录时发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    test_fund_portfolio_data()
