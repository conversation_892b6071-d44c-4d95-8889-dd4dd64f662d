import { createRouter, createWebHistory } from 'vue-router'

// 路由懒加载
const Layout = () => import('@/views/layout/Layout.vue')
const FundList = () => import('@/views/fund/FundList.vue')
const FundDetail = () => import('@/views/fund/FundDetail.vue')
const FundCompare = () => import('@/views/fund/FundCompare.vue')
const ReportCenter = () => import('@/views/report/ReportCenter.vue')

const routes = [
  {
    path: '/',
    redirect: '/funds'
  },
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: 'funds',
        name: 'FundList',
        component: FundList,
        meta: {
          title: '基金列表',
          icon: 'List'
        }
      },
      {
        path: 'funds/:code',
        name: 'FundDetail',
        component: FundDetail,
        meta: {
          title: '基金详情',
          icon: 'Document'
        }
      },
      {
        path: 'compare',
        name: 'FundCompare',
        component: FundCompare,
        meta: {
          title: '基金对比',
          icon: 'TrendCharts'
        }
      },
      {
        path: 'reports',
        name: 'ReportCenter',
        component: ReportCenter,
        meta: {
          title: '报告中心',
          icon: 'Files'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 基金分析系统`
  }
  next()
})

export default router
