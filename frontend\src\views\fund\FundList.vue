<template>
  <div class="fund-list-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">基金列表</h2>
      <p class="page-description">查看和管理所有基金信息</p>
    </div>

    <!-- 搜索和筛选工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索基金代码或名称"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="selectedType"
          placeholder="基金类型"
          style="width: 150px"
          clearable
          @change="handleFilter"
        >
          <el-option label="股票型" value="stock" />
          <el-option label="债券型" value="bond" />
          <el-option label="混合型" value="hybrid" />
          <el-option label="货币型" value="money" />
          <el-option label="指数型" value="index" />
        </el-select>
        
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="resetFilters">
          <el-icon><RefreshLeft /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        
        <el-button type="primary" @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-container">
      <div class="stats-card">
        <div class="stats-value">{{ fundStore.total }}</div>
        <div class="stats-label">基金总数</div>
      </div>
      <div class="stats-card">
        <div class="stats-value">{{ selectedFunds.length }}</div>
        <div class="stats-label">已选择</div>
      </div>
      <div class="stats-card">
        <div class="stats-value">{{ activeFundsCount }}</div>
        <div class="stats-label">活跃基金</div>
      </div>
    </div>

    <!-- 基金数据表格 -->
    <div class="table-container">
      <el-table
        ref="fundTable"
        v-loading="fundStore.loading"
        :data="fundStore.fundList"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column
          prop="code"
          label="基金代码"
          width="120"
          sortable
        >
          <template #default="{ row }">
            <el-link 
              type="primary" 
              @click.stop="goToDetail(row.code)"
            >
              {{ formatFundCode(row.code) }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="name"
          label="基金名称"
          min-width="200"
          show-overflow-tooltip
        />
        
        <el-table-column
          prop="manager"
          label="基金经理"
          width="120"
          show-overflow-tooltip
        />
        
        <el-table-column
          prop="type"
          label="基金类型"
          width="100"
        >
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ formatFundType(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="scale"
          label="基金规模"
          width="120"
          sortable
        >
          <template #default="{ row }">
            {{ formatFundScale(row.scale) }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="netValue"
          label="单位净值"
          width="100"
          sortable
        >
          <template #default="{ row }">
            {{ formatNumber(row.netValue, 4) }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="dayReturn"
          label="日涨跌幅"
          width="100"
          sortable
        >
          <template #default="{ row }">
            <span :class="getReturnClass(row.dayReturn)">
              {{ formatReturn(row.dayReturn) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="yearReturn"
          label="近一年收益"
          width="120"
          sortable
        >
          <template #default="{ row }">
            <span :class="getReturnClass(row.yearReturn)">
              {{ formatReturn(row.yearReturn) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="180"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="goToDetail(row.code)"
            >
              详情
            </el-button>
            
            <el-button
              size="small"
              @click.stop="addToCompare(row)"
              :disabled="isInCompare(row.code)"
            >
              {{ isInCompare(row.code) ? '已添加' : '对比' }}
            </el-button>
            
            <el-dropdown @command="handleCommand">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`export-${row.code}`">
                    导出报告
                  </el-dropdown-item>
                  <el-dropdown-item :command="`analyze-${row.code}`">
                    重新分析
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="fundStore.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对比基金浮动面板 -->
    <div v-if="selectedFunds.length > 0" class="compare-panel">
      <div class="compare-content">
        <span>已选择 {{ selectedFunds.length }} 只基金进行对比</span>
        <div class="compare-actions">
          <el-button size="small" @click="clearCompare">清空</el-button>
          <el-button 
            type="primary" 
            size="small" 
            @click="goToCompare"
            :disabled="selectedFunds.length < 2"
          >
            开始对比
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useFundStore } from '@/stores/fund'
import { 
  formatNumber, formatReturn, formatFundCode, 
  formatFundType, formatFundScale 
} from '@/utils/format'
import { exportFundList } from '@/utils/export'
import {
  Search, RefreshLeft, Download, Refresh, ArrowDown
} from '@element-plus/icons-vue'

const router = useRouter()
const fundStore = useFundStore()

// 响应式数据
const searchKeyword = ref('')
const selectedType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedFunds = ref([])

// 计算属性
const activeFundsCount = computed(() => fundStore.activeFunds.length)

// 方法
const handleSearch = async () => {
  currentPage.value = 1
  await fundStore.searchFunds(searchKeyword.value)
}

const handleFilter = async () => {
  currentPage.value = 1
  await fundStore.filterFunds({ type: selectedType.value })
}

const resetFilters = async () => {
  searchKeyword.value = ''
  selectedType.value = ''
  currentPage.value = 1
  fundStore.resetFilters()
  await fundStore.fetchFundList()
}

const handleRefresh = async () => {
  await fundStore.fetchFundList()
  ElMessage.success('数据刷新完成')
}

const handleExport = async () => {
  try {
    await exportFundList(fundStore.fundList, 'excel')
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  }
}

const handleSelectionChange = (selection) => {
  selectedFunds.value = selection
}

const handleRowClick = (row) => {
  goToDetail(row.code)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  fundStore.pageSize = size
  fundStore.fetchFundList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fundStore.currentPage = page
  fundStore.fetchFundList()
}

const goToDetail = (fundCode) => {
  router.push(`/funds/${fundCode}`)
}

const addToCompare = (fund) => {
  try {
    fundStore.addToCompare(fund)
    ElMessage.success(`已添加 ${fund.name} 到对比列表`)
  } catch (error) {
    ElMessage.warning(error.message)
  }
}

const isInCompare = (fundCode) => {
  return fundStore.selectedFundCodes.includes(fundCode)
}

const clearCompare = () => {
  fundStore.clearCompare()
  selectedFunds.value = []
  ElMessage.success('已清空对比列表')
}

const goToCompare = () => {
  if (selectedFunds.value.length < 2) {
    ElMessage.warning('至少选择2只基金进行对比')
    return
  }
  router.push('/compare')
}

const handleCommand = (command) => {
  const [action, fundCode] = command.split('-')
  
  if (action === 'export') {
    // 导出单个基金报告
    ElMessage.info('导出功能开发中...')
  } else if (action === 'analyze') {
    // 重新分析基金
    ElMessage.info('重新分析功能开发中...')
  }
}

// 工具函数
const getTypeTagType = (type) => {
  const typeMap = {
    'stock': 'danger',
    'bond': 'success',
    'hybrid': 'warning',
    'money': 'info',
    'index': 'primary'
  }
  return typeMap[type] || ''
}

const getReturnClass = (value) => {
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-danger'
  return 'text-regular'
}

// 监听器
watch([currentPage, pageSize], () => {
  fundStore.currentPage = currentPage.value
  fundStore.pageSize = pageSize.value
})

// 生命周期
onMounted(async () => {
  await fundStore.fetchFundList()
})
</script>

<style scoped>
.fund-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: var(--spacing-lg);
}

.toolbar {
  background: var(--bg-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  margin-bottom: var(--spacing-md);
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.table-container {
  flex: 1;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-lg) 0;
}

.compare-panel {
  position: fixed;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--primary-color);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-dark);
  z-index: 1000;
}

.compare-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.compare-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.text-success {
  color: var(--success-color);
  font-weight: 600;
}

.text-danger {
  color: var(--danger-color);
  font-weight: 600;
}

.text-regular {
  color: var(--text-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .compare-panel {
    left: var(--spacing-md);
    right: var(--spacing-md);
  }
}
</style>
