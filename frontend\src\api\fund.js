import { http } from '@/utils/request'

/**
 * 基金相关API接口
 */

// 获取基金列表
export const getFundList = (params) => {
  return http.get('/funds', params)
}

// 获取基金详情
export const getFundDetail = (fundCode) => {
  return http.get(`/funds/${fundCode}`)
}

// 获取基金持仓数据
export const getFundPortfolio = (fundCode, params = {}) => {
  return http.get(`/funds/${fundCode}/portfolio`, params)
}

// 获取基金业绩数据
export const getFundPerformance = (fundCode, params = {}) => {
  return http.get(`/funds/${fundCode}/performance`, params)
}

// 获取基金净值数据
export const getFundNetValue = (fundCode, params = {}) => {
  return http.get(`/funds/${fundCode}/net-value`, params)
}

// 基金对比分析
export const compareFunds = (fundCodes) => {
  return http.post('/funds/compare', { fundCodes })
}

// 搜索基金
export const searchFunds = (keyword) => {
  return http.get('/funds/search', { keyword })
}

// 获取基金类型列表
export const getFundTypes = () => {
  return http.get('/funds/types')
}

// 获取基金经理列表
export const getFundManagers = () => {
  return http.get('/funds/managers')
}

// 获取基金公司列表
export const getFundCompanies = () => {
  return http.get('/funds/companies')
}
