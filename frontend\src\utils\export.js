/**
 * 数据导出工具函数
 */

import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

/**
 * 导出Excel文件
 * @param {Array} data 数据数组
 * @param {string} filename 文件名
 * @param {string} sheetName 工作表名称
 */
export const exportToExcel = (data, filename = 'export', sheetName = 'Sheet1') => {
  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 创建工作表
    const ws = XLSX.utils.json_to_sheet(data)
    
    // 设置列宽
    const colWidths = []
    if (data.length > 0) {
      Object.keys(data[0]).forEach((key, index) => {
        const maxLength = Math.max(
          key.length,
          ...data.map(row => String(row[key] || '').length)
        )
        colWidths[index] = { wch: Math.min(maxLength + 2, 50) }
      })
      ws['!cols'] = colWidths
    }
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, sheetName)
    
    // 生成Excel文件
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    
    // 保存文件
    const blob = new Blob([excelBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    saveAs(blob, `${filename}.xlsx`)
    
    return true
  } catch (error) {
    console.error('导出Excel失败:', error)
    throw new Error('导出Excel失败')
  }
}

/**
 * 导出CSV文件
 * @param {Array} data 数据数组
 * @param {string} filename 文件名
 */
export const exportToCSV = (data, filename = 'export') => {
  try {
    if (!data || data.length === 0) {
      throw new Error('没有数据可导出')
    }
    
    // 获取表头
    const headers = Object.keys(data[0])
    
    // 构建CSV内容
    const csvContent = [
      headers.join(','), // 表头
      ...data.map(row => 
        headers.map(header => {
          const value = row[header] || ''
          // 处理包含逗号或引号的值
          if (String(value).includes(',') || String(value).includes('"')) {
            return `"${String(value).replace(/"/g, '""')}"`
          }
          return value
        }).join(',')
      )
    ].join('\n')
    
    // 添加BOM以支持中文
    const BOM = '\uFEFF'
    const blob = new Blob([BOM + csvContent], { 
      type: 'text/csv;charset=utf-8' 
    })
    
    saveAs(blob, `${filename}.csv`)
    
    return true
  } catch (error) {
    console.error('导出CSV失败:', error)
    throw new Error('导出CSV失败')
  }
}

/**
 * 导出JSON文件
 * @param {Object|Array} data 数据
 * @param {string} filename 文件名
 */
export const exportToJSON = (data, filename = 'export') => {
  try {
    const jsonString = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonString], { 
      type: 'application/json;charset=utf-8' 
    })
    
    saveAs(blob, `${filename}.json`)
    
    return true
  } catch (error) {
    console.error('导出JSON失败:', error)
    throw new Error('导出JSON失败')
  }
}

/**
 * 导出基金列表数据
 * @param {Array} fundList 基金列表
 * @param {string} format 导出格式 (excel|csv)
 */
export const exportFundList = (fundList, format = 'excel') => {
  const exportData = fundList.map(fund => ({
    '基金代码': fund.code,
    '基金名称': fund.name,
    '基金经理': fund.manager,
    '基金类型': fund.type,
    '基金规模(亿元)': fund.scale,
    '单位净值': fund.netValue,
    '累计净值': fund.totalNetValue,
    '日涨跌幅(%)': fund.dayReturn,
    '近一年收益率(%)': fund.yearReturn,
    '成立日期': fund.establishDate,
    '管理费率(%)': fund.managementFee,
    '托管费率(%)': fund.custodyFee
  }))
  
  const filename = `基金列表_${new Date().toISOString().slice(0, 10)}`
  
  if (format === 'csv') {
    return exportToCSV(exportData, filename)
  } else {
    return exportToExcel(exportData, filename, '基金列表')
  }
}

/**
 * 导出基金详情数据
 * @param {Object} fundDetail 基金详情
 * @param {string} format 导出格式
 */
export const exportFundDetail = (fundDetail, format = 'excel') => {
  const { basicInfo, portfolio, performance } = fundDetail
  
  if (format === 'excel') {
    // 创建多个工作表
    const wb = XLSX.utils.book_new()
    
    // 基本信息工作表
    const basicData = [
      { '项目': '基金代码', '值': basicInfo.code },
      { '项目': '基金名称', '值': basicInfo.name },
      { '项目': '基金经理', '值': basicInfo.manager },
      { '项目': '基金类型', '值': basicInfo.type },
      { '项目': '基金规模', '值': basicInfo.scale },
      { '项目': '成立日期', '值': basicInfo.establishDate },
      { '项目': '管理费率', '值': basicInfo.managementFee },
      { '项目': '托管费率', '值': basicInfo.custodyFee }
    ]
    const basicWs = XLSX.utils.json_to_sheet(basicData)
    XLSX.utils.book_append_sheet(wb, basicWs, '基本信息')
    
    // 持仓信息工作表
    if (portfolio && portfolio.length > 0) {
      const portfolioData = portfolio.map((item, index) => ({
        '序号': index + 1,
        '股票代码': item.stockCode,
        '股票名称': item.stockName,
        '持仓比例(%)': item.weight,
        '持仓市值(万元)': item.marketValue,
        '持仓股数(万股)': item.shares
      }))
      const portfolioWs = XLSX.utils.json_to_sheet(portfolioData)
      XLSX.utils.book_append_sheet(wb, portfolioWs, '持仓明细')
    }
    
    // 业绩信息工作表
    if (performance && performance.length > 0) {
      const performanceData = performance.map(item => ({
        '日期': item.date,
        '单位净值': item.netValue,
        '累计净值': item.totalNetValue,
        '日涨跌幅(%)': item.dayReturn,
        '累计收益率(%)': item.totalReturn
      }))
      const performanceWs = XLSX.utils.json_to_sheet(performanceData)
      XLSX.utils.book_append_sheet(wb, performanceWs, '业绩表现')
    }
    
    // 导出文件
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    const filename = `${basicInfo.name}_${basicInfo.code}_详情_${new Date().toISOString().slice(0, 10)}`
    saveAs(blob, `${filename}.xlsx`)
    
    return true
  } else {
    // CSV格式只导出持仓信息
    if (portfolio && portfolio.length > 0) {
      const filename = `${basicInfo.name}_${basicInfo.code}_持仓_${new Date().toISOString().slice(0, 10)}`
      return exportToCSV(portfolio, filename)
    }
  }
}

/**
 * 导出基金对比数据
 * @param {Array} compareData 对比数据
 * @param {string} format 导出格式
 */
export const exportFundCompare = (compareData, format = 'excel') => {
  const filename = `基金对比分析_${new Date().toISOString().slice(0, 10)}`
  
  if (format === 'csv') {
    return exportToCSV(compareData, filename)
  } else {
    return exportToExcel(compareData, filename, '基金对比')
  }
}

/**
 * 下载模板文件
 * @param {string} templateType 模板类型
 */
export const downloadTemplate = (templateType) => {
  const templates = {
    fundList: [
      {
        '基金代码': '000001.OF',
        '基金名称': '华夏成长混合',
        '备注': '请按此格式填写基金信息'
      }
    ]
  }
  
  const templateData = templates[templateType]
  if (templateData) {
    const filename = `${templateType}_模板`
    return exportToExcel(templateData, filename, '模板')
  } else {
    throw new Error('模板类型不存在')
  }
}
