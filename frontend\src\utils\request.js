import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 在请求发送之前做一些处理
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    
    // 可以在这里添加token
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response
    
    // 统一处理响应数据
    if (data.code === 200) {
      return data
    } else {
      // 业务错误处理
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  error => {
    console.error('响应错误:', error)
    
    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data.message || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 可以在这里处理登录跳转
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = data.message || `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message) {
      message = error.message
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 封装常用的请求方法
export const http = {
  get(url, params = {}) {
    return request({
      method: 'get',
      url,
      params
    })
  },
  
  post(url, data = {}) {
    return request({
      method: 'post',
      url,
      data
    })
  },
  
  put(url, data = {}) {
    return request({
      method: 'put',
      url,
      data
    })
  },
  
  delete(url, params = {}) {
    return request({
      method: 'delete',
      url,
      params
    })
  },
  
  upload(url, formData) {
    return request({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 带loading的请求
export const httpWithLoading = {
  async get(url, params = {}, loadingText = '加载中...') {
    const loading = ElLoading.service({ text: loadingText })
    try {
      const result = await http.get(url, params)
      return result
    } finally {
      loading.close()
    }
  },
  
  async post(url, data = {}, loadingText = '提交中...') {
    const loading = ElLoading.service({ text: loadingText })
    try {
      const result = await http.post(url, data)
      return result
    } finally {
      loading.close()
    }
  }
}

export default request
