<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-icon">
        <el-icon size="120"><DocumentDelete /></el-icon>
      </div>
      
      <h1 class="error-title">404</h1>
      <h2 class="error-subtitle">页面未找到</h2>
      
      <p class="error-description">
        抱歉，您访问的页面不存在或已被移除。
      </p>
      
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>
      
      <div class="helpful-links">
        <h3>您可能想要：</h3>
        <ul>
          <li>
            <el-link type="primary" @click="$router.push('/funds')">
              查看基金列表
            </el-link>
          </li>
          <li>
            <el-link type="primary" @click="$router.push('/compare')">
              基金对比分析
            </el-link>
          </li>
          <li>
            <el-link type="primary" @click="$router.push('/reports')">
              查看分析报告
            </el-link>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { DocumentDelete, HomeFilled, Back } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/funds')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--bg-page);
  padding: var(--spacing-lg);
}

.not-found-content {
  text-align: center;
  max-width: 500px;
  background: var(--bg-color);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
}

.error-icon {
  color: var(--text-placeholder);
  margin-bottom: var(--spacing-lg);
}

.error-title {
  font-size: 72px;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 var(--spacing-md) 0;
  line-height: 1;
}

.error-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  font-weight: 600;
}

.error-description {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.helpful-links {
  text-align: left;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-lighter);
}

.helpful-links h3 {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.helpful-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.helpful-links li {
  margin-bottom: var(--spacing-sm);
  padding-left: var(--spacing-md);
  position: relative;
}

.helpful-links li::before {
  content: '•';
  color: var(--primary-color);
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-content {
    padding: var(--spacing-lg);
  }
  
  .error-title {
    font-size: 48px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .helpful-links {
    text-align: center;
  }
}
</style>
