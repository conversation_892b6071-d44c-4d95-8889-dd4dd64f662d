#!/usr/bin/env python3
"""
测试169102.OF的数据获取逻辑
"""

import logging
import os
import time
from dotenv import load_dotenv
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data, convert_fund_code_suffix

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def test_169102_fund_logic():
    """测试169102.OF的完整数据获取逻辑"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    fund_code = "169102.OF"
    
    logger.info(f"=== 测试基金 {fund_code} 的数据获取逻辑 ===")
    
    # 1. 测试后缀转换函数
    logger.info(f"\n1. 测试后缀转换函数")
    converted_codes = convert_fund_code_suffix(fund_code)
    logger.info(f"转换结果: {fund_code} -> {converted_codes}")
    
    # 2. 获取基金基本信息
    logger.info(f"\n2. 获取基金基本信息")
    start_time = time.time()
    fund_data = fetch_fund_data(fund_code)
    basic_time = time.time() - start_time
    
    if fund_data is None:
        logger.error(f"❌ 基金 {fund_code} 无法获取基本信息")
        return
    
    logger.info(f"✅ 基金基本信息获取成功 (耗时: {basic_time:.3f}秒)")
    logger.info(f"  基金名称: {fund_data.get('fund_name', '未知')}")
    logger.info(f"  基金经理: {fund_data.get('fund_manager', '未知')}")
    logger.info(f"  报告期: {fund_data.get('report_end_date', '未知')}")
    
    # 3. 获取持仓数据（当前逻辑，不包含转换）
    report_end_date = fund_data.get('report_end_date')
    if not report_end_date or report_end_date == "未知报告日期":
        logger.warning(f"报告期无效，无法测试持仓数据获取")
        return
    
    logger.info(f"\n3. 测试持仓数据获取（当前逻辑）")
    start_time = time.time()
    portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)
    portfolio_time = time.time() - start_time
    
    logger.info(f"持仓数据查询耗时: {portfolio_time:.3f}秒")
    
    if portfolio_data is None:
        logger.warning(f"⚠️  基金 {fund_code} 持仓数据获取失败")
        
        # 4. 手动测试转换逻辑
        logger.info(f"\n4. 手动测试转换逻辑")
        for converted_code in converted_codes:
            logger.info(f"尝试转换代码: {converted_code}")
            start_time = time.time()
            converted_portfolio_data = fetch_fund_portfolio_data(converted_code, report_end_date)
            converted_time = time.time() - start_time
            
            logger.info(f"转换代码查询耗时: {converted_time:.3f}秒")
            
            if converted_portfolio_data is None:
                logger.info(f"  ❌ 转换代码 {converted_code} 也无持仓数据")
            elif not converted_portfolio_data:
                logger.info(f"  ⚠️  转换代码 {converted_code} 持仓数据为空")
            else:
                logger.info(f"  ✅ 转换代码 {converted_code} 成功获取 {len(converted_portfolio_data)} 条持仓数据")
                
                # 显示前3条持仓
                logger.info(f"  前3大持仓:")
                for i, holding in enumerate(converted_portfolio_data[:3], 1):
                    logger.info(f"    {i}. {holding.get('symbol', 'N/A')} - "
                              f"{holding.get('stock_name', 'N/A')} - "
                              f"占比: {holding.get('stk_mkv_ratio', 'N/A')}%")
                break
    elif not portfolio_data:
        logger.info(f"✅ 基金 {fund_code} 持仓数据为空列表")
    else:
        logger.info(f"✅ 基金 {fund_code} 成功获取 {len(portfolio_data)} 条持仓数据")
        
        # 显示前3条持仓
        logger.info(f"前3大持仓:")
        for i, holding in enumerate(portfolio_data[:3], 1):
            logger.info(f"  {i}. {holding.get('symbol', 'N/A')} - "
                      f"{holding.get('stock_name', 'N/A')} - "
                      f"占比: {holding.get('stk_mkv_ratio', 'N/A')}%")
    
    # 5. 数据库验证
    logger.info(f"\n5. 数据库验证")
    from sqlalchemy import create_engine, text
    
    # 构建数据库连接
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # 检查基本信息
            basic_query = text("""
                SELECT fund_code, fund_name, fund_manager, report_year, report_quarter
                FROM fund_quarterly_data
                WHERE fund_code = :fund_code
                ORDER BY report_year DESC, report_quarter DESC
                LIMIT 3
            """)
            
            result = conn.execute(basic_query, {"fund_code": fund_code})
            basic_records = result.fetchall()
            
            logger.info(f"基本信息记录:")
            for record in basic_records:
                logger.info(f"  {record[0]} | {record[1]} | {record[2]} | {record[3]}Q{record[4]}")
            
            # 检查持仓数据
            codes_to_check = [fund_code] + converted_codes
            for code in codes_to_check:
                portfolio_query = text("""
                    SELECT ts_code, COUNT(*) as count, 
                           MIN(end_date) as earliest, 
                           MAX(end_date) as latest
                    FROM tushare_fund_portfolio
                    WHERE ts_code = :code
                    GROUP BY ts_code
                """)
                
                result = conn.execute(portfolio_query, {"code": code})
                portfolio_record = result.fetchone()
                
                if portfolio_record:
                    logger.info(f"持仓数据 {code}: {portfolio_record[1]}条记录 ({portfolio_record[2]} ~ {portfolio_record[3]})")
                else:
                    logger.info(f"持仓数据 {code}: 无记录")
                    
    except Exception as e:
        logger.error(f"数据库验证失败: {e}")
    
    logger.info(f"\n=== 测试完成 ===")

if __name__ == "__main__":
    setup_logging()
    test_169102_fund_logic()
