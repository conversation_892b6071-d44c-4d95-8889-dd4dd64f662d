#!/usr/bin/env python3
"""
验证基金代码转换的合理性 - 检查是否存在数据冲突风险
"""

import logging
import os
from dotenv import load_dotenv
from sqlalchemy import create_engine, text

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def validate_fund_identity_conflicts():
    """验证基金代码转换是否会造成身份冲突"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    # 构建数据库连接字符串
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            logger.info("=== 基金代码转换合理性验证 ===\n")
            
            # 测试案例：检查可能存在冲突的基金对
            test_pairs = [
                ("163302.OF", "163302.SZ"),
                ("159150.OF", "159150.SZ"),
                ("159201.OF", "159201.SZ"),
                ("510050.OF", "510050.SH"),
            ]
            
            for of_code, converted_code in test_pairs:
                logger.info(f"检查基金对: {of_code} vs {converted_code}")
                logger.info("-" * 60)
                
                # 1. 检查基本信息是否都存在
                basic_info_query = text("""
                    SELECT fund_code, fund_name, fund_manager, fund_manager_company, fund_type
                    FROM fund_quarterly_data
                    WHERE fund_code IN (:of_code, :converted_code) AND is_latest = true
                    ORDER BY fund_code
                """)
                
                result = conn.execute(basic_info_query, {
                    "of_code": of_code,
                    "converted_code": converted_code
                })
                basic_infos = result.fetchall()
                
                logger.info(f"基本信息查询结果:")
                of_info = None
                converted_info = None
                
                for info in basic_infos:
                    logger.info(f"  {info[0]}: {info[1]} | {info[2]} | {info[3]} | {info[4]}")
                    if info[0] == of_code:
                        of_info = info
                    elif info[0] == converted_code:
                        converted_info = info
                
                # 2. 分析冲突风险
                if of_info and converted_info:
                    logger.warning(f"⚠️  发现潜在冲突：两个代码都有基本信息！")
                    logger.warning(f"   {of_code}: {of_info[1]} ({of_info[2]})")
                    logger.warning(f"   {converted_code}: {converted_info[1]} ({converted_info[2]})")
                    
                    # 检查是否是同一只基金
                    if of_info[1] == converted_info[1] and of_info[2] == converted_info[2]:
                        logger.info(f"✅ 基金名称和经理相同，可能是同一只基金的不同代码")
                    else:
                        logger.error(f"❌ 基金名称或经理不同，这是两只不同的基金！转换有风险！")
                        
                elif of_info and not converted_info:
                    logger.info(f"✅ 只有{of_code}有基本信息，转换到{converted_code}相对安全")
                elif not of_info and converted_info:
                    logger.info(f"✅ 只有{converted_code}有基本信息，从{of_code}转换过来合理")
                else:
                    logger.info(f"ℹ️  两个代码都没有基本信息")
                
                # 3. 检查持仓数据是否都存在
                portfolio_query = text("""
                    SELECT ts_code, COUNT(*) as record_count,
                           MIN(end_date) as earliest_date,
                           MAX(end_date) as latest_date
                    FROM tushare_fund_portfolio
                    WHERE ts_code IN (:of_code, :converted_code)
                    GROUP BY ts_code
                    ORDER BY ts_code
                """)
                
                result = conn.execute(portfolio_query, {
                    "of_code": of_code,
                    "converted_code": converted_code
                })
                portfolio_infos = result.fetchall()
                
                logger.info(f"持仓数据查询结果:")
                of_portfolio = None
                converted_portfolio = None
                
                for info in portfolio_infos:
                    logger.info(f"  {info[0]}: {info[1]}条记录 ({info[2]} ~ {info[3]})")
                    if info[0] == of_code:
                        of_portfolio = info
                    elif info[0] == converted_code:
                        converted_portfolio = info
                
                if of_portfolio and converted_portfolio:
                    logger.warning(f"⚠️  两个代码都有持仓数据，需要进一步验证")
                    
                    # 检查最新持仓是否相似
                    similarity_query = text("""
                        WITH of_holdings AS (
                            SELECT symbol, stk_mkv_ratio
                            FROM tushare_fund_portfolio
                            WHERE ts_code = :of_code
                            AND end_date = (SELECT MAX(end_date) FROM tushare_fund_portfolio WHERE ts_code = :of_code)
                            ORDER BY stk_mkv_ratio DESC
                            LIMIT 5
                        ),
                        converted_holdings AS (
                            SELECT symbol, stk_mkv_ratio
                            FROM tushare_fund_portfolio
                            WHERE ts_code = :converted_code
                            AND end_date = (SELECT MAX(end_date) FROM tushare_fund_portfolio WHERE ts_code = :converted_code)
                            ORDER BY stk_mkv_ratio DESC
                            LIMIT 5
                        )
                        SELECT 
                            COALESCE(o.symbol, c.symbol) as symbol,
                            o.stk_mkv_ratio as of_ratio,
                            c.stk_mkv_ratio as converted_ratio
                        FROM of_holdings o
                        FULL OUTER JOIN converted_holdings c ON o.symbol = c.symbol
                        ORDER BY COALESCE(o.stk_mkv_ratio, 0) + COALESCE(c.stk_mkv_ratio, 0) DESC
                    """)
                    
                    result = conn.execute(similarity_query, {
                        "of_code": of_code,
                        "converted_code": converted_code
                    })
                    holdings_comparison = result.fetchall()
                    
                    if holdings_comparison:
                        logger.info(f"前5大持仓对比:")
                        common_holdings = 0
                        total_holdings = len(holdings_comparison)
                        
                        for holding in holdings_comparison:
                            symbol = holding[0]
                            of_ratio = holding[1] if holding[1] is not None else 0
                            converted_ratio = holding[2] if holding[2] is not None else 0
                            
                            if of_ratio > 0 and converted_ratio > 0:
                                common_holdings += 1
                                logger.info(f"    {symbol}: {of_ratio:.2f}% vs {converted_ratio:.2f}% ✅")
                            elif of_ratio > 0:
                                logger.info(f"    {symbol}: {of_ratio:.2f}% vs 0% (仅{of_code})")
                            else:
                                logger.info(f"    {symbol}: 0% vs {converted_ratio:.2f}% (仅{converted_code})")
                        
                        similarity_ratio = common_holdings / max(total_holdings, 1)
                        logger.info(f"持仓相似度: {similarity_ratio:.1%} ({common_holdings}/{total_holdings})")
                        
                        if similarity_ratio > 0.6:
                            logger.info(f"✅ 持仓相似度较高，可能是同一只基金")
                        else:
                            logger.warning(f"⚠️  持仓相似度较低，可能是不同基金")
                
                logger.info("")
            
            # 4. 全局风险评估
            logger.info("=== 全局风险评估 ===")
            
            # 检查有多少基金同时存在.OF和.SZ/.SH版本
            conflict_query = text("""
                WITH base_codes AS (
                    SELECT SUBSTRING(fund_code FROM '^(.+)\\.[A-Z]+$') as base_code,
                           fund_code,
                           fund_name
                    FROM fund_quarterly_data
                    WHERE fund_code ~ '^.+\\.[A-Z]+$' AND is_latest = true
                ),
                potential_conflicts AS (
                    SELECT base_code,
                           COUNT(*) as code_count,
                           STRING_AGG(fund_code || ':' || fund_name, ' | ') as all_versions
                    FROM base_codes
                    GROUP BY base_code
                    HAVING COUNT(*) > 1
                )
                SELECT base_code, code_count, all_versions
                FROM potential_conflicts
                ORDER BY code_count DESC, base_code
                LIMIT 20
            """)
            
            result = conn.execute(conflict_query)
            conflicts = result.fetchall()
            
            if conflicts:
                logger.warning(f"发现 {len(conflicts)} 个基础代码存在多个后缀版本:")
                for conflict in conflicts[:10]:  # 只显示前10个
                    logger.warning(f"  {conflict[0]}: {conflict[1]}个版本 - {conflict[2]}")
                    
                if len(conflicts) > 10:
                    logger.warning(f"  ... 还有 {len(conflicts) - 10} 个类似情况")
            else:
                logger.info("✅ 未发现明显的基金代码冲突")
                
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    validate_fund_identity_conflicts()
