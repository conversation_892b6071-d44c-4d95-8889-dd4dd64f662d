#!/usr/bin/env python3
"""
测试季度配置文件与基金代码后缀转换功能的兼容性
"""

import logging
import os
import time
from dotenv import load_dotenv
from src.data_fetcher import (
    fetch_fund_data, 
    fetch_fund_data_with_config, 
    fetch_fund_portfolio_data,
    get_quarterly_data_config,
    convert_fund_code_suffix
)

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def test_quarterly_config_with_conversion():
    """测试季度配置与后缀转换功能的兼容性"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    logger.info("=== 测试季度配置文件与基金代码后缀转换功能兼容性 ===")
    
    # 测试基金列表
    test_funds = ["169102.OF", "163302.OF"]
    
    # 1. 检查当前季度配置
    logger.info("\n1. 检查当前季度配置")
    config_year, config_quarter = get_quarterly_data_config()
    logger.info(f"配置文件设置: {config_year}年Q{config_quarter}")
    
    if config_year and config_quarter:
        # 计算配置的报告期截止日
        if config_quarter == 1:
            config_report_date = f"{config_year}-03-31"
        elif config_quarter == 2:
            config_report_date = f"{config_year}-06-30"
        elif config_quarter == 3:
            config_report_date = f"{config_year}-09-30"
        elif config_quarter == 4:
            config_report_date = f"{config_year}-12-31"
        else:
            config_report_date = "无效配置"
        
        logger.info(f"配置对应的报告期截止日: {config_report_date}")
    else:
        logger.warning("未找到有效的季度配置，将使用最新数据")
        config_report_date = None
    
    for fund_code in test_funds:
        logger.info(f"\n{'='*70}")
        logger.info(f"测试基金: {fund_code}")
        logger.info(f"{'='*70}")
        
        # 2. 测试 fetch_fund_data_with_config 函数
        logger.info(f"\n2. 测试 fetch_fund_data_with_config 函数")
        start_time = time.time()
        fund_data_config = fetch_fund_data_with_config(fund_code)
        config_time = time.time() - start_time
        
        if fund_data_config is None:
            logger.error(f"❌ 使用配置文件无法获取基金 {fund_code} 的基本信息")
            continue
        
        logger.info(f"✅ 配置文件方式获取成功 (耗时: {config_time:.3f}秒)")
        logger.info(f"  基金名称: {fund_data_config.get('fund_name', '未知')}")
        logger.info(f"  基金经理: {fund_data_config.get('fund_manager', '未知')}")
        logger.info(f"  报告期: {fund_data_config.get('report_end_date', '未知')}")
        
        # 3. 对比直接获取最新数据
        logger.info(f"\n3. 对比直接获取最新数据")
        start_time = time.time()
        fund_data_latest = fetch_fund_data(fund_code)
        latest_time = time.time() - start_time
        
        if fund_data_latest is None:
            logger.error(f"❌ 直接方式无法获取基金 {fund_code} 的基本信息")
        else:
            logger.info(f"✅ 直接方式获取成功 (耗时: {latest_time:.3f}秒)")
            logger.info(f"  基金名称: {fund_data_latest.get('fund_name', '未知')}")
            logger.info(f"  基金经理: {fund_data_latest.get('fund_manager', '未知')}")
            logger.info(f"  报告期: {fund_data_latest.get('report_end_date', '未知')}")
            
            # 比较两种方式的结果
            config_date = fund_data_config.get('report_end_date')
            latest_date = fund_data_latest.get('report_end_date')
            
            if config_date == latest_date:
                logger.info(f"✅ 两种方式获取的报告期一致: {config_date}")
            else:
                logger.warning(f"⚠️  两种方式获取的报告期不同:")
                logger.warning(f"   配置文件方式: {config_date}")
                logger.warning(f"   直接方式: {latest_date}")
        
        # 4. 测试配置文件方式的持仓数据获取（含转换）
        config_report_date = fund_data_config.get('report_end_date')
        if not config_report_date or config_report_date == "未知报告日期":
            logger.warning(f"配置文件获取的报告期无效，跳过持仓数据测试")
            continue
        
        logger.info(f"\n4. 测试配置文件方式的持仓数据获取（含转换）")
        logger.info(f"使用报告期: {config_report_date}")
        
        start_time = time.time()
        portfolio_data_config = fetch_fund_portfolio_data(fund_code, config_report_date)
        portfolio_config_time = time.time() - start_time
        
        logger.info(f"持仓数据查询耗时: {portfolio_config_time:.3f}秒")
        
        if portfolio_data_config is None:
            logger.warning(f"⚠️  配置报告期 {config_report_date} 下，基金 {fund_code} 持仓数据获取失败")
        elif not portfolio_data_config:
            logger.info(f"ℹ️  配置报告期 {config_report_date} 下，基金 {fund_code} 持仓数据为空")
        else:
            logger.info(f"✅ 配置报告期 {config_report_date} 下，基金 {fund_code} 成功获取 {len(portfolio_data_config)} 条持仓数据")
            
            # 显示前3条持仓
            logger.info(f"前3大持仓:")
            for i, holding in enumerate(portfolio_data_config[:3], 1):
                logger.info(f"  {i}. {holding.get('symbol', 'N/A')} - "
                          f"{holding.get('stock_name', 'N/A')} - "
                          f"占比: {holding.get('stk_mkv_ratio', 'N/A')}%")
        
        # 5. 手动验证转换逻辑在指定报告期的工作情况
        logger.info(f"\n5. 手动验证转换逻辑在指定报告期的工作情况")
        converted_codes = convert_fund_code_suffix(fund_code)
        logger.info(f"转换代码: {fund_code} -> {converted_codes}")
        
        for converted_code in converted_codes:
            logger.info(f"\n测试转换代码: {converted_code}")
            start_time = time.time()
            converted_portfolio = fetch_fund_portfolio_data(converted_code, config_report_date)
            converted_time = time.time() - start_time
            
            logger.info(f"转换代码查询耗时: {converted_time:.3f}秒")
            
            if converted_portfolio is None:
                logger.info(f"  ❌ 转换代码 {converted_code} 在报告期 {config_report_date} 无持仓数据")
            elif not converted_portfolio:
                logger.info(f"  ⚠️  转换代码 {converted_code} 在报告期 {config_report_date} 持仓数据为空")
            else:
                logger.info(f"  ✅ 转换代码 {converted_code} 在报告期 {config_report_date} 有 {len(converted_portfolio)} 条持仓数据")
                
                # 显示前2条持仓
                for i, holding in enumerate(converted_portfolio[:2], 1):
                    logger.info(f"    {i}. {holding.get('symbol', 'N/A')} - "
                              f"{holding.get('stock_name', 'N/A')} - "
                              f"占比: {holding.get('stk_mkv_ratio', 'N/A')}%")
        
        # 6. 数据库直接验证
        logger.info(f"\n6. 数据库直接验证指定报告期的数据存在情况")
        verify_database_data(fund_code, converted_codes, config_report_date)

def verify_database_data(fund_code, converted_codes, report_date):
    """直接查询数据库验证指定报告期的数据"""
    logger = logging.getLogger(__name__)
    
    try:
        from sqlalchemy import create_engine, text
        
        # 构建数据库连接
        db_host = os.getenv("DB_HOST")
        db_port = os.getenv("DB_PORT")
        db_name = os.getenv("DB_NAME")
        db_user = os.getenv("DB_USER")
        db_password = os.getenv("DB_PASSWORD")
        
        database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # 检查所有相关代码在指定报告期的持仓数据
            codes_to_check = [fund_code] + converted_codes
            
            for code in codes_to_check:
                portfolio_query = text("""
                    SELECT ts_code, COUNT(*) as count, end_date
                    FROM tushare_fund_portfolio
                    WHERE ts_code = :code AND end_date = :report_date
                    GROUP BY ts_code, end_date
                """)
                
                result = conn.execute(portfolio_query, {
                    "code": code,
                    "report_date": report_date
                })
                portfolio_record = result.fetchone()
                
                if portfolio_record:
                    logger.info(f"数据库验证 {code} 在 {report_date}: {portfolio_record[1]}条记录")
                else:
                    logger.info(f"数据库验证 {code} 在 {report_date}: 无记录")
                    
                # 检查该代码在其他日期的数据情况
                other_dates_query = text("""
                    SELECT end_date, COUNT(*) as count
                    FROM tushare_fund_portfolio
                    WHERE ts_code = :code
                    GROUP BY end_date
                    ORDER BY end_date DESC
                    LIMIT 5
                """)
                
                result = conn.execute(other_dates_query, {"code": code})
                other_dates = result.fetchall()
                
                if other_dates:
                    logger.info(f"  {code} 的其他可用日期:")
                    for date_record in other_dates:
                        logger.info(f"    {date_record[0]}: {date_record[1]}条记录")
                else:
                    logger.info(f"  {code} 无任何持仓数据")
                    
    except Exception as e:
        logger.error(f"数据库验证失败: {e}")

if __name__ == "__main__":
    setup_logging()
    test_quarterly_config_with_conversion()
