import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 应用状态
  const sidebarCollapsed = ref(false)
  const theme = ref('light')
  const device = ref('desktop')
  const loading = ref(false)
  
  // 面包屑导航
  const breadcrumbs = ref([])
  
  // 全局配置
  const config = ref({
    title: '基金分析系统',
    version: '1.0.0',
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api'
  })

  // Actions
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
  }
  
  const setTheme = (newTheme) => {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
  }
  
  const setDevice = (newDevice) => {
    device.value = newDevice
    
    // 移动端自动收起侧边栏
    if (newDevice === 'mobile') {
      sidebarCollapsed.value = true
    }
  }
  
  const setLoading = (isLoading) => {
    loading.value = isLoading
  }
  
  const setBreadcrumbs = (crumbs) => {
    breadcrumbs.value = crumbs
  }
  
  const addBreadcrumb = (crumb) => {
    breadcrumbs.value.push(crumb)
  }
  
  const updateConfig = (newConfig) => {
    Object.assign(config.value, newConfig)
  }

  return {
    // 状态
    sidebarCollapsed,
    theme,
    device,
    loading,
    breadcrumbs,
    config,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    setDevice,
    setLoading,
    setBreadcrumbs,
    addBreadcrumb,
    updateConfig
  }
})
