import { http } from '@/utils/request'

/**
 * 报告相关API接口
 */

// 获取基金分析报告
export const getFundReport = (fundCode) => {
  return http.get(`/reports/${fundCode}`)
}

// 获取报告列表
export const getReportList = (params) => {
  return http.get('/reports', params)
}

// 导出报告
export const exportReport = (fundCode, format = 'excel') => {
  return http.post('/reports/export', {
    fundCode,
    format
  })
}

// 批量导出报告
export const batchExportReports = (fundCodes, format = 'excel') => {
  return http.post('/reports/batch-export', {
    fundCodes,
    format
  })
}

// 下载报告文件
export const downloadReport = (reportId) => {
  return http.get(`/reports/download/${reportId}`, {}, {
    responseType: 'blob'
  })
}

// 生成对比报告
export const generateCompareReport = (fundCodes) => {
  return http.post('/reports/compare', {
    fundCodes
  })
}
