<template>
  <div class="base-chart-container">
    <!-- 图表标题栏 -->
    <div v-if="showHeader" class="chart-header">
      <div class="header-left">
        <h3 v-if="title" class="chart-title">{{ title }}</h3>
        <p v-if="subtitle" class="chart-subtitle">{{ subtitle }}</p>
      </div>
      
      <div class="header-right">
        <slot name="header-actions">
          <el-button-group v-if="showControls">
            <el-button 
              v-if="showRefresh"
              size="small" 
              @click="handleRefresh"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
            
            <el-button 
              v-if="showFullscreen"
              size="small" 
              @click="toggleFullscreen"
            >
              <el-icon><FullScreen /></el-icon>
            </el-button>
            
            <el-button 
              v-if="showDownload"
              size="small" 
              @click="downloadChart"
            >
              <el-icon><Download /></el-icon>
            </el-button>
          </el-button-group>
        </slot>
      </div>
    </div>

    <!-- 图表内容区域 -->
    <div 
      ref="chartContainer"
      class="chart-content"
      :class="{ 'is-fullscreen': isFullscreen }"
      :style="chartStyle"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="chart-loading">
        <el-loading-spinner />
        <p>{{ loadingText }}</p>
      </div>
      
      <!-- 空数据状态 -->
      <div v-else-if="isEmpty" class="chart-empty">
        <el-icon class="empty-icon"><DocumentDelete /></el-icon>
        <p>{{ emptyText }}</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="hasError" class="chart-error">
        <el-icon class="error-icon"><Warning /></el-icon>
        <p>{{ errorText }}</p>
        <el-button size="small" @click="handleRetry">重试</el-button>
      </div>
      
      <!-- ECharts图表 -->
      <v-chart
        v-else
        ref="chartRef"
        :option="chartOption"
        :theme="theme"
        :autoresize="autoresize"
        :loading="chartLoading"
        :loading-options="loadingOptions"
        @click="handleChartClick"
        @dblclick="handleChartDblclick"
        @mouseover="handleChartMouseover"
        @mouseout="handleChartMouseout"
        @legendselectchanged="handleLegendSelectChanged"
        @datazoom="handleDataZoom"
        class="chart-instance"
      />
    </div>

    <!-- 图表说明 -->
    <div v-if="showFooter && description" class="chart-footer">
      <p class="chart-description">{{ description }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { getResponsiveChartOption } from '@/utils/chart'
import {
  Refresh, FullScreen, Download, DocumentDelete, Warning
} from '@element-plus/icons-vue'

// 注册ECharts渲染器
use([CanvasRenderer])

// Props定义
const props = defineProps({
  // 图表配置
  option: {
    type: Object,
    default: () => ({})
  },
  
  // 图表主题
  theme: {
    type: String,
    default: 'default'
  },
  
  // 标题相关
  title: {
    type: String,
    default: ''
  },
  
  subtitle: {
    type: String,
    default: ''
  },
  
  description: {
    type: String,
    default: ''
  },
  
  // 尺寸相关
  width: {
    type: [String, Number],
    default: '100%'
  },
  
  height: {
    type: [String, Number],
    default: '400px'
  },
  
  // 状态相关
  loading: {
    type: Boolean,
    default: false
  },
  
  loadingText: {
    type: String,
    default: '图表加载中...'
  },
  
  isEmpty: {
    type: Boolean,
    default: false
  },
  
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  
  hasError: {
    type: Boolean,
    default: false
  },
  
  errorText: {
    type: String,
    default: '图表加载失败'
  },
  
  // 功能开关
  showHeader: {
    type: Boolean,
    default: true
  },
  
  showFooter: {
    type: Boolean,
    default: false
  },
  
  showControls: {
    type: Boolean,
    default: true
  },
  
  showRefresh: {
    type: Boolean,
    default: true
  },
  
  showFullscreen: {
    type: Boolean,
    default: true
  },
  
  showDownload: {
    type: Boolean,
    default: true
  },
  
  // ECharts配置
  autoresize: {
    type: Boolean,
    default: true
  },
  
  chartLoading: {
    type: Boolean,
    default: false
  },
  
  loadingOptions: {
    type: Object,
    default: () => ({
      text: '加载中...',
      color: '#409eff',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    })
  },
  
  // 响应式配置
  responsive: {
    type: Boolean,
    default: true
  }
})

// Emits定义
const emit = defineEmits([
  'refresh',
  'download',
  'retry',
  'chart-click',
  'chart-dblclick',
  'chart-mouseover',
  'chart-mouseout',
  'legend-select-changed',
  'data-zoom'
])

// 响应式数据
const chartRef = ref()
const chartContainer = ref()
const isFullscreen = ref(false)
const containerWidth = ref(0)

// 计算属性
const chartStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

const chartOption = computed(() => {
  if (!props.option || Object.keys(props.option).length === 0) {
    return {}
  }
  
  if (props.responsive && containerWidth.value > 0) {
    return getResponsiveChartOption(props.option, containerWidth.value)
  }
  
  return props.option
})

// 事件处理
const handleRefresh = () => {
  emit('refresh')
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
  
  // 延迟调整图表大小
  setTimeout(() => {
    chartRef.value?.resize()
  }, 300)
}

const downloadChart = () => {
  if (chartRef.value) {
    const url = chartRef.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.download = `${props.title || 'chart'}.png`
    link.href = url
    link.click()
  }
  
  emit('download')
}

const handleRetry = () => {
  emit('retry')
}

const handleChartClick = (params) => {
  emit('chart-click', params)
}

const handleChartDblclick = (params) => {
  emit('chart-dblclick', params)
}

const handleChartMouseover = (params) => {
  emit('chart-mouseover', params)
}

const handleChartMouseout = (params) => {
  emit('chart-mouseout', params)
}

const handleLegendSelectChanged = (params) => {
  emit('legend-select-changed', params)
}

const handleDataZoom = (params) => {
  emit('data-zoom', params)
}

// 响应式处理
const updateContainerWidth = () => {
  if (chartContainer.value) {
    containerWidth.value = chartContainer.value.offsetWidth
  }
}

const resizeObserver = new ResizeObserver(() => {
  updateContainerWidth()
})

// 暴露方法
const resize = () => {
  chartRef.value?.resize()
}

const getDataURL = (options) => {
  return chartRef.value?.getDataURL(options)
}

const getConnectedDataURL = (options) => {
  return chartRef.value?.getConnectedDataURL(options)
}

const convertToPixel = (finder, value) => {
  return chartRef.value?.convertToPixel(finder, value)
}

const convertFromPixel = (finder, value) => {
  return chartRef.value?.convertFromPixel(finder, value)
}

const containPixel = (finder, value) => {
  return chartRef.value?.containPixel(finder, value)
}

const showLoading = (options) => {
  chartRef.value?.showLoading(options)
}

const hideLoading = () => {
  chartRef.value?.hideLoading()
}

const clear = () => {
  chartRef.value?.clear()
}

const dispose = () => {
  chartRef.value?.dispose()
}

defineExpose({
  resize,
  getDataURL,
  getConnectedDataURL,
  convertToPixel,
  convertFromPixel,
  containPixel,
  showLoading,
  hideLoading,
  clear,
  dispose,
  chartRef
})

// 生命周期
onMounted(() => {
  updateContainerWidth()
  if (chartContainer.value) {
    resizeObserver.observe(chartContainer.value)
  }
})

onUnmounted(() => {
  if (chartContainer.value) {
    resizeObserver.unobserve(chartContainer.value)
  }
  
  if (isFullscreen.value) {
    document.body.style.overflow = ''
  }
})

// 监听器
watch(
  () => props.option,
  () => {
    // 当配置变化时，延迟调整图表大小
    setTimeout(() => {
      chartRef.value?.resize()
    }, 100)
  },
  { deep: true }
)
</script>

<style scoped>
.base-chart-container {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-lighter);
  background: var(--bg-page);
}

.header-left {
  flex: 1;
}

.chart-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.chart-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.header-right {
  flex-shrink: 0;
}

.chart-content {
  position: relative;
  transition: all 0.3s ease;
}

.chart-content.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: var(--bg-color);
  width: 100vw !important;
  height: 100vh !important;
}

.chart-instance {
  width: 100%;
  height: 100%;
}

.chart-loading,
.chart-empty,
.chart-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: var(--text-secondary);
}

.chart-loading p,
.chart-empty p,
.chart-error p {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-sm);
}

.empty-icon,
.error-icon {
  font-size: 48px;
  opacity: 0.5;
}

.error-icon {
  color: var(--danger-color);
}

.chart-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-lighter);
  background: var(--bg-page);
}

.chart-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .header-right {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}
</style>
