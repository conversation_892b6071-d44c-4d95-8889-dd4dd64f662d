#!/usr/bin/env python3
"""
验证跨表数据转换的一致性风险
检查基本信息和持仓数据是否真的属于同一只基金
"""

import logging
import os
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from datetime import datetime

def setup_logging():
    """配置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

def validate_cross_table_data_consistency():
    """验证跨表数据转换的一致性"""
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    # 构建数据库连接字符串
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    db_user = os.getenv("DB_USER")
    db_password = os.getenv("DB_PASSWORD")
    
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            logger.info("=== 跨表数据一致性验证 ===\n")
            
            # 重点测试案例
            test_cases = [
                {
                    "basic_code": "163302.OF",
                    "portfolio_code": "163302.SZ",
                    "fund_name": "大摩资源优选"
                },
                {
                    "basic_code": "159150.OF", 
                    "portfolio_code": "159150.SZ",
                    "fund_name": "易方达深证50ETF"
                },
                {
                    "basic_code": "159201.OF",
                    "portfolio_code": "159201.SZ", 
                    "fund_name": "华夏国证自由现金流ETF"
                }
            ]
            
            for case in test_cases:
                basic_code = case["basic_code"]
                portfolio_code = case["portfolio_code"]
                expected_name = case["fund_name"]
                
                logger.info(f"验证基金: {basic_code} <-> {portfolio_code}")
                logger.info("=" * 70)
                
                # 1. 获取基本信息
                basic_info_query = text("""
                    SELECT fund_code, fund_name, fund_manager, fund_manager_company, 
                           fund_type, report_year, report_quarter, market_outlook, 
                           market_analysis, quarterly_return
                    FROM fund_quarterly_data
                    WHERE fund_code = :basic_code AND is_latest = true
                    LIMIT 1
                """)
                
                result = conn.execute(basic_info_query, {"basic_code": basic_code})
                basic_info = result.fetchone()
                
                if not basic_info:
                    logger.error(f"❌ 未找到基金 {basic_code} 的基本信息")
                    continue
                
                logger.info(f"基本信息 ({basic_code}):")
                logger.info(f"  基金名称: {basic_info[1]}")
                logger.info(f"  基金经理: {basic_info[2]}")
                logger.info(f"  基金公司: {basic_info[3]}")
                logger.info(f"  基金类型: {basic_info[4]}")
                logger.info(f"  报告期: {basic_info[5]}Q{basic_info[6]}")
                
                # 2. 获取持仓数据概况
                portfolio_info_query = text("""
                    SELECT ts_code, COUNT(*) as holding_count,
                           MAX(end_date) as latest_date,
                           SUM(stk_mkv_ratio) as total_ratio
                    FROM tushare_fund_portfolio
                    WHERE ts_code = :portfolio_code
                    GROUP BY ts_code
                """)
                
                result = conn.execute(portfolio_info_query, {"portfolio_code": portfolio_code})
                portfolio_info = result.fetchone()
                
                if not portfolio_info:
                    logger.error(f"❌ 未找到基金 {portfolio_code} 的持仓数据")
                    continue
                
                logger.info(f"\n持仓数据概况 ({portfolio_code}):")
                logger.info(f"  持仓记录数: {portfolio_info[1]}")
                logger.info(f"  最新日期: {portfolio_info[2]}")
                logger.info(f"  总持仓比例: {portfolio_info[3]:.2f}%")
                
                # 3. 获取最新持仓详情
                latest_holdings_query = text("""
                    SELECT symbol, mkv, stk_mkv_ratio, end_date
                    FROM tushare_fund_portfolio
                    WHERE ts_code = :portfolio_code
                    AND end_date = (
                        SELECT MAX(end_date) 
                        FROM tushare_fund_portfolio 
                        WHERE ts_code = :portfolio_code
                    )
                    ORDER BY stk_mkv_ratio DESC
                    LIMIT 5
                """)
                
                result = conn.execute(latest_holdings_query, {"portfolio_code": portfolio_code})
                latest_holdings = result.fetchall()
                
                logger.info(f"\n最新前5大持仓 ({portfolio_info[2]}):")
                for i, holding in enumerate(latest_holdings, 1):
                    logger.info(f"  {i}. {holding[0]} - 占比: {holding[2]:.2f}%")
                
                # 4. 一致性分析
                logger.info(f"\n一致性分析:")
                
                # 4.1 基金名称一致性
                if basic_info[1] == expected_name:
                    logger.info(f"✅ 基金名称与预期一致: {basic_info[1]}")
                else:
                    logger.warning(f"⚠️  基金名称与预期不符: 实际={basic_info[1]}, 预期={expected_name}")
                
                # 4.2 时间一致性检查
                basic_report_date = f"{basic_info[5]}-{basic_info[6]:02d}"
                portfolio_date = portfolio_info[2].strftime("%Y-%m-%d") if portfolio_info[2] else "未知"
                
                # 将季度转换为大概的月份进行比较
                quarter_to_month = {1: "03", 2: "06", 3: "09", 4: "12"}
                expected_month = quarter_to_month.get(basic_info[6], "未知")
                
                if expected_month in portfolio_date:
                    logger.info(f"✅ 报告期时间基本一致: 基本信息={basic_report_date}, 持仓={portfolio_date}")
                else:
                    logger.warning(f"⚠️  报告期时间可能不一致: 基本信息={basic_report_date}, 持仓={portfolio_date}")
                
                # 4.3 基金类型与持仓特征一致性
                fund_type = basic_info[4] or ""
                holding_count = portfolio_info[1]
                total_ratio = portfolio_info[3] or 0
                
                logger.info(f"基金类型分析:")
                logger.info(f"  类型: {fund_type}")
                logger.info(f"  持仓数量: {holding_count}")
                logger.info(f"  总持仓比例: {total_ratio:.2f}%")
                
                # 基于基金类型判断持仓特征是否合理
                if "ETF" in fund_type or "指数" in fund_type:
                    if holding_count >= 20 and total_ratio > 80:
                        logger.info(f"✅ ETF/指数基金持仓特征正常（分散持仓，高仓位）")
                    else:
                        logger.warning(f"⚠️  ETF/指数基金持仓特征异常（持仓数={holding_count}, 仓位={total_ratio:.1f}%）")
                elif "灵活配置" in fund_type or "混合" in fund_type:
                    if 10 <= holding_count <= 100 and 60 <= total_ratio <= 95:
                        logger.info(f"✅ 灵活配置基金持仓特征正常")
                    else:
                        logger.warning(f"⚠️  灵活配置基金持仓特征需关注")
                
                # 5. 风险评估
                risk_score = 0
                risk_factors = []
                
                if basic_info[1] != expected_name:
                    risk_score += 3
                    risk_factors.append("基金名称不符预期")
                
                if expected_month not in portfolio_date:
                    risk_score += 2
                    risk_factors.append("报告期时间不一致")
                
                if ("ETF" in fund_type and holding_count < 20) or (holding_count == 0):
                    risk_score += 3
                    risk_factors.append("持仓特征与基金类型不符")
                
                if total_ratio < 50:
                    risk_score += 2
                    risk_factors.append("总持仓比例过低")
                
                logger.info(f"\n风险评估:")
                if risk_score == 0:
                    logger.info(f"✅ 数据一致性良好，风险评分: {risk_score}/10")
                elif risk_score <= 3:
                    logger.info(f"🟡 数据一致性可接受，风险评分: {risk_score}/10")
                    logger.info(f"   风险因素: {', '.join(risk_factors)}")
                else:
                    logger.error(f"❌ 数据一致性存在问题，风险评分: {risk_score}/10")
                    logger.error(f"   风险因素: {', '.join(risk_factors)}")
                
                logger.info("\n" + "="*70 + "\n")
            
            # 6. 系统性风险评估
            logger.info("=== 系统性风险评估 ===")
            
            # 检查有多少基金存在跨表转换的情况
            cross_table_query = text("""
                WITH basic_funds AS (
                    SELECT SUBSTRING(fund_code FROM '^(.+)\\.[A-Z]+$') as base_code,
                           fund_code, fund_name
                    FROM fund_quarterly_data
                    WHERE fund_code ~ '^.+\\.[A-Z]+$' AND is_latest = true
                ),
                portfolio_funds AS (
                    SELECT SUBSTRING(ts_code FROM '^(.+)\\.[A-Z]+$') as base_code,
                           ts_code
                    FROM (
                        SELECT DISTINCT ts_code 
                        FROM tushare_fund_portfolio
                    ) p
                    WHERE ts_code ~ '^.+\\.[A-Z]+$'
                )
                SELECT 
                    b.base_code,
                    b.fund_code as basic_code,
                    b.fund_name,
                    p.ts_code as portfolio_code,
                    CASE 
                        WHEN b.fund_code = p.ts_code THEN 'DIRECT_MATCH'
                        WHEN b.fund_code != p.ts_code THEN 'CROSS_TABLE_CONVERSION'
                        ELSE 'NO_PORTFOLIO'
                    END as match_type
                FROM basic_funds b
                LEFT JOIN portfolio_funds p ON b.base_code = p.base_code
                WHERE p.base_code IS NOT NULL
                ORDER BY match_type, b.base_code
            """)
            
            result = conn.execute(cross_table_query)
            cross_table_results = result.fetchall()
            
            direct_matches = 0
            cross_conversions = 0
            conversion_examples = []
            
            for row in cross_table_results:
                if row[4] == 'DIRECT_MATCH':
                    direct_matches += 1
                elif row[4] == 'CROSS_TABLE_CONVERSION':
                    cross_conversions += 1
                    if len(conversion_examples) < 10:
                        conversion_examples.append(row)
            
            total_with_portfolio = direct_matches + cross_conversions
            conversion_ratio = cross_conversions / total_with_portfolio if total_with_portfolio > 0 else 0
            
            logger.info(f"系统性风险统计:")
            logger.info(f"  直接匹配基金: {direct_matches} 个")
            logger.info(f"  需要跨表转换基金: {cross_conversions} 个")
            logger.info(f"  跨表转换比例: {conversion_ratio:.1%}")
            
            if conversion_ratio > 0.1:  # 超过10%
                logger.warning(f"⚠️  跨表转换比例较高 ({conversion_ratio:.1%})，需要重点关注数据一致性")
            else:
                logger.info(f"✅ 跨表转换比例在可接受范围内")
            
            if conversion_examples:
                logger.info(f"\n跨表转换示例:")
                for example in conversion_examples[:5]:
                    logger.info(f"  {example[1]} -> {example[3]} ({example[2]})")
                    
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    validate_cross_table_data_consistency()
