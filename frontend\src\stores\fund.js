import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as fundApi from '@/api/fund'

export const useFundStore = defineStore('fund', () => {
  // 状态定义
  const fundList = ref([])
  const currentFund = ref(null)
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)
  
  // 筛选条件
  const filters = ref({
    keyword: '',
    fundType: '',
    scale: '',
    manager: ''
  })
  
  // 选中的基金（用于对比）
  const selectedFunds = ref([])

  // 计算属性
  const activeFunds = computed(() => 
    fundList.value.filter(fund => fund.status === 'active')
  )
  
  const hasMore = computed(() => 
    fundList.value.length < total.value
  )
  
  const selectedFundCodes = computed(() => 
    selectedFunds.value.map(fund => fund.code)
  )

  // Actions
  const fetchFundList = async (params = {}) => {
    loading.value = true
    try {
      const queryParams = {
        page: currentPage.value,
        pageSize: pageSize.value,
        ...filters.value,
        ...params
      }
      
      const response = await fundApi.getFundList(queryParams)
      
      if (response.code === 200) {
        fundList.value = response.data.list
        total.value = response.data.total
        currentPage.value = response.data.page
      }
    } catch (error) {
      console.error('获取基金列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const fetchFundDetail = async (fundCode) => {
    loading.value = true
    try {
      const response = await fundApi.getFundDetail(fundCode)
      
      if (response.code === 200) {
        currentFund.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取基金详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const searchFunds = async (keyword) => {
    filters.value.keyword = keyword
    currentPage.value = 1
    await fetchFundList()
  }
  
  const filterFunds = async (newFilters) => {
    Object.assign(filters.value, newFilters)
    currentPage.value = 1
    await fetchFundList()
  }
  
  const loadMore = async () => {
    if (hasMore.value && !loading.value) {
      currentPage.value += 1
      const response = await fundApi.getFundList({
        page: currentPage.value,
        pageSize: pageSize.value,
        ...filters.value
      })
      
      if (response.code === 200) {
        fundList.value.push(...response.data.list)
      }
    }
  }
  
  const addToCompare = (fund) => {
    if (selectedFunds.value.length >= 5) {
      throw new Error('最多只能选择5只基金进行对比')
    }
    
    if (!selectedFundCodes.value.includes(fund.code)) {
      selectedFunds.value.push(fund)
    }
  }
  
  const removeFromCompare = (fundCode) => {
    const index = selectedFunds.value.findIndex(fund => fund.code === fundCode)
    if (index > -1) {
      selectedFunds.value.splice(index, 1)
    }
  }
  
  const clearCompare = () => {
    selectedFunds.value = []
  }
  
  const resetFilters = () => {
    filters.value = {
      keyword: '',
      fundType: '',
      scale: '',
      manager: ''
    }
    currentPage.value = 1
  }

  return {
    // 状态
    fundList,
    currentFund,
    loading,
    total,
    currentPage,
    pageSize,
    filters,
    selectedFunds,
    
    // 计算属性
    activeFunds,
    hasMore,
    selectedFundCodes,
    
    // 方法
    fetchFundList,
    fetchFundDetail,
    searchFunds,
    filterFunds,
    loadMore,
    addToCompare,
    removeFromCompare,
    clearCompare,
    resetFilters
  }
})
