# 开发环境配置

# 应用标题
VITE_APP_TITLE=基金分析系统

# API基础URL
VITE_API_BASE_URL=http://localhost:8000/api

# 应用端口
VITE_PORT=3000

# 是否开启Mock数据
VITE_USE_MOCK=true

# 是否开启代理
VITE_USE_PROXY=true

# 代理目标地址
VITE_PROXY_TARGET=http://localhost:8000

# 是否开启HTTPS
VITE_HTTPS=false

# 是否自动打开浏览器
VITE_OPEN=true

# 构建输出目录
VITE_OUTPUT_DIR=dist

# 公共路径
VITE_PUBLIC_PATH=/

# 是否开启gzip压缩
VITE_BUILD_GZIP=false

# 是否开启分析报告
VITE_BUILD_ANALYZER=false

# 是否删除console
VITE_DROP_CONSOLE=false

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否开启PWA
VITE_USE_PWA=false

# CDN基础URL
VITE_CDN_URL=

# 上传文件大小限制(MB)
VITE_UPLOAD_SIZE_LIMIT=10

# 分页默认大小
VITE_PAGE_SIZE=20

# 表格最大高度
VITE_TABLE_MAX_HEIGHT=600

# 图表默认主题
VITE_CHART_THEME=default

# 是否开启水印
VITE_USE_WATERMARK=false

# 水印文本
VITE_WATERMARK_TEXT=基金分析系统

# 是否开启错误监控
VITE_USE_ERROR_MONITOR=true

# 错误监控DSN
VITE_ERROR_MONITOR_DSN=

# 是否开启性能监控
VITE_USE_PERFORMANCE_MONITOR=true

# API请求超时时间(ms)
VITE_REQUEST_TIMEOUT=30000

# 是否开启请求重试
VITE_REQUEST_RETRY=true

# 请求重试次数
VITE_REQUEST_RETRY_COUNT=3

# 是否开启缓存
VITE_USE_CACHE=true

# 缓存过期时间(ms)
VITE_CACHE_EXPIRE=300000

# 是否开启调试模式
VITE_DEBUG=true

# 日志级别 (error, warn, info, debug)
VITE_LOG_LEVEL=debug
